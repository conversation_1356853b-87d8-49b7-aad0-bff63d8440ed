(function (window) {
  'use strict';

  /*
   * 简易 API 请求封装（基于 fetch）
   * 
   * 用法示例：
   *   API.get('/api/articles/list', { page: 1, pagesize: 10 }).then(function (res) {
   *     if (res.code === 1) {
   *       console.log(res.data.items);
   *     } else {
   *       alert(res.msg || '请求失败');
   *     }
   *   });
   *
   * 约定：
   * - 自动从 Cookie 读取 token（若存在），并放到请求头 Token 里
   * - 自动将对象 body 序列化为 JSON（FormData 则原样传递）
   * - 统一返回后端的 JSON 对象 { code, msg, data }
   */

  // 从本地存储获取登录 Token（与 header.html 保持一致）
  function getAuthToken() {
    var t = '';
    try { t = localStorage.getItem('user_token') || ''; } catch (e) {}
    if (!t) { try { t = sessionStorage.getItem('user_token') || ''; } catch (e) {} }
    return t || '';
  }

  // 将对象序列化为查询字符串 a=1&b=2
  function serialize(params) {
    if (!params) return '';
    var str = [];
    for (var key in params) {
      if (!Object.prototype.hasOwnProperty.call(params, key)) continue;
      var value = params[key];
      if (value === undefined || value === null) continue;
      str.push(encodeURIComponent(key) + '=' + encodeURIComponent(String(value)));
    }
    return str.join('&');
  }

  // 在 URL 后拼接查询参数
  function buildUrl(url, params) {
    var qs = serialize(params);
    if (!qs) return url;
    return url + (url.indexOf('?') === -1 ? '?' : '&') + qs;
  }

  // 统一请求方法
  async function request(url, options) {
    options = options || {};
    var method = (options.method || 'GET').toUpperCase();
    var headers = options.headers || {};
    var params = options.params || null;
    var body = options.body || null;

    // 使用FastAdmin标准的Token头，同时保持Authorization头的兼容性
    var token = getAuthToken();
    if (token) {
      // FastAdmin标准：Token头
      if (!headers['Token']) headers['Token'] = token;
      // 兼容性：Authorization头
      if (!headers['Authorization']) headers['Authorization'] = 'Bearer ' + token;
    }
    headers['X-Requested-With'] = headers['X-Requested-With'] || 'XMLHttpRequest';

    var finalUrl = buildUrl(url, params);
    var fetchOptions = { method: method, headers: headers, credentials: 'same-origin' };

    // 处理请求体：对象 -> JSON，FormData 原样传
    if (body) {
      if (!(body instanceof FormData)) {
        headers['Content-Type'] = headers['Content-Type'] || 'application/json;charset=utf-8';
        fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
      } else {
        fetchOptions.body = body;
      }
    }

    // 发起请求并尝试解析为 JSON
    var resp = await fetch(finalUrl, fetchOptions);
    var json;
    try {
      json = await resp.json();
    } catch (e) {
      json = { code: 0, msg: '响应不是有效的 JSON', data: null };
    }
    return json;
  }

  // 暴露的简易 API
  var API = {
    request: request, // 自由定制
    get: function (url, params, headers) {
      return request(url, { method: 'GET', params: params || null, headers: headers || {} });
    },
    post: function (url, body, headers) {
      return request(url, { method: 'POST', body: body || null, headers: headers || {} });
    },
    put: function (url, body, headers) {
      return request(url, { method: 'PUT', body: body || null, headers: headers || {} });
    },
    del: function (url, body, headers) {
      return request(url, { method: 'DELETE', body: body || null, headers: headers || {} });
    }
  };

  // 绑定到全局，页面里可直接用 window.API
  window.API = API;
})(window);


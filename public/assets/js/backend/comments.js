define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'comments/index' + location.search,
                    add_url: 'comments/add',
                    edit_url: 'comments/edit',
                    del_url: 'comments/del',
                    multi_url: 'comments/multi',
                    import_url: 'comments/import',
                    table: 'comments',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'article_id', title: __('Article_id')},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'parent_id', title: __('Parent_id')},
                        {field: 'child_id', title: __('Child_id')},
                        {field: 'status', title: __('Status')},
                        {field: 'created_at', title: __('Created_at')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

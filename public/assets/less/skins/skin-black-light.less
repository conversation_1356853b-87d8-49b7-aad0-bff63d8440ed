/*
 * Skin: Black light
 * -----------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

.skin-black-light {
    //Navbar & Logo
    .main-header {
        .box-shadow(0px 1px 1px rgba(0, 0, 0, 0.05));

        .navbar-toggle {
            color: #333;
        }

        .navbar-brand {
            color: #333;
            border-right: 1px solid #eee;
        }

        .navbar {
            .navbar-variant(#222d32; #fff; #f6f6f6; rgba(0, 0, 0, 0.3));

            > .sidebar-toggle {
                color: #333;
                border-right: 1px solid #eee;
            }

            .navbar-nav {
                > li > a {
                    //border-right: 1px solid #eee;
                    color: #fff;
                }
            }

            .navbar-custom-menu .navbar-nav,
            .navbar-right {
                > li {
                    > a {
                        //border-left: 1px solid #eee;
                        border-left: none;
                        border-right-width: 0;
                    }
                }
            }
        }

        .logo {
            .logo-variant(#222d32; #fff);
            //border-right: 1px solid #eee;
            @media (max-width: @screen-header-collapse) {
                .logo-variant(#222d32; #fff);
                border-right: none;
            }
        }

        li.user-header {
            background-color: #222d32;
        }
    }

    //Content Header
    .content-header {
        background: transparent;
        box-shadow: none;
    }

    //Create the sidebar skin
    .skin-light-sidebar(#222d32);

    .sidebar-menu > li {
        > a {
            border-left: 3px solid transparent;
            padding-left: 12px;
        }
    }

    @media (min-width: @screen-sm) {
        &.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
            margin-left: -3px;
        }
    }

    .main-sidebar {
        .box-shadow(7px 0 14px rgba(0, 0, 0, .03));
    }

    .content-wrapper, .main-footer {
        border-left: none;
    }

    &.multiplenav {
        @media (max-width: @screen-header-collapse) {
            .sidebar .mobilenav a.btn-app {
                background: #eceff3;
                color: #757575;

                &.active {
                    background: #222d32;
                    color: #fff;
                }
            }
        }
    }
}

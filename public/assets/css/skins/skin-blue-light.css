/*
 * Skin: Blue
 * ----------
 */
.skin-blue-light .main-header .navbar {
  background-color: #4e73df;
}
.skin-blue-light .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-blue-light .main-header .navbar .nav > li > a:hover,
.skin-blue-light .main-header .navbar .nav > li > a:active,
.skin-blue-light .main-header .navbar .nav > li > a:focus,
.skin-blue-light .main-header .navbar .nav .open > a,
.skin-blue-light .main-header .navbar .nav .open > a:hover,
.skin-blue-light .main-header .navbar .nav .open > a:focus,
.skin-blue-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-blue-light .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-blue-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-blue-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-blue-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-blue-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #3862db;
}
@media (max-width: 767px) {
  .skin-blue-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-blue-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-blue-light .main-header .navbar .dropdown-menu li a:hover {
    background: #3862db;
  }
}
.skin-blue-light .main-header .logo {
  background-color: #4e73df;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-blue-light .main-header .logo:hover {
  background-color: #4a70de;
}
.skin-blue-light .main-header li.user-header {
  background-color: #4e73df;
}
.skin-blue-light .content-header {
  background: transparent;
}
.skin-blue-light .wrapper,
.skin-blue-light .main-sidebar,
.skin-blue-light .left-side {
  background-color: #f9fafc;
}
.skin-blue-light .content-wrapper,
.skin-blue-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-blue-light .user-panel > .info,
.skin-blue-light .user-panel > .info > a {
  color: #444;
}
.skin-blue-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-blue-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-blue-light .sidebar-menu > li:hover > a,
.skin-blue-light .sidebar-menu > li.active > a {
  color: #000;
  background: #f4f4f5;
  border-left-color: #4e73df;
}
.skin-blue-light .sidebar-menu > li.active {
  border-left-color: #4e73df;
}
.skin-blue-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-blue-light .sidebar a {
  color: #444;
}
.skin-blue-light .sidebar a:hover {
  text-decoration: none;
}
.skin-blue-light .treeview-menu > li > a {
  color: #777;
}
.skin-blue-light .treeview-menu > li.active > a,
.skin-blue-light .treeview-menu > li > a:hover {
  color: #000;
}
.skin-blue-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-blue-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-blue-light .sidebar-form input[type="text"],
.skin-blue-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-blue-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-blue-light .sidebar-form input[type="text"]:focus,
.skin-blue-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-blue-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-blue-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-blue-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
.skin-blue-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-blue-light.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-blue-light .main-footer {
  border-top-color: #d2d6de;
}
.skin-blue-light .main-sidebar {
  -webkit-box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
  box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
}
.skin-blue-light .content-wrapper,
.skin-blue-light .main-footer {
  border-left: none;
}
@media (max-width: 767px) {
  .skin-blue-light.multiplenav .sidebar .mobilenav a.btn-app {
    background: #eceff3;
    color: #757575;
  }
  .skin-blue-light.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #4e73df;
    color: #fff;
  }
}
/*# sourceMappingURL=skin-blue-light.css.map */
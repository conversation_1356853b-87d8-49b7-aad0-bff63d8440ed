<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Article_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-article_id" data-rule="required" data-source="article/index" class="form-control selectpage" name="row[article_id]" type="text" value="{$row.article_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Expert_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-expert_id" data-rule="required" data-source="expert/index" class="form-control selectpage" name="row[expert_id]" type="text" value="{$row.expert_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type" data-rule="required" class="form-control" name="row[type]" type="number" value="{$row.type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Annotation_text')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-annotation_text" data-rule="required" class="form-control " rows="5" name="row[annotation_text]" cols="50">{$row.annotation_text|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Target_text')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-target_text" class="form-control " rows="5" name="row[target_text]" cols="50">{$row.target_text|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Created_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-created_at" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[created_at]" type="text" value="{:$row.created_at?datetime($row.created_at):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

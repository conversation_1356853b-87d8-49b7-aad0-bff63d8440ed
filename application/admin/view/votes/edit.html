<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Article_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-article_id" data-rule="required" data-source="article/index" class="form-control selectpage" name="row[article_id]" type="text" value="{$row.article_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Annotation_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-annotation_id" data-rule="required" data-source="annotation/index" class="form-control selectpage" name="row[annotation_id]" type="text" value="{$row.annotation_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vote_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vote_type" data-rule="required" class="form-control" name="row[vote_type]" type="number" value="{$row.vote_type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Created_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-created_at" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[created_at]" type="text" value="{:$row.created_at?datetime($row.created_at):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

{include file="common/header" /}

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">调试示例页面</h3>
                </div>
                <div class="panel-body">
                    <h4>1. 调试数据显示</h4>
                    <pre>{$debug_data|json_encode}</pre>
                    
                    <h4>2. 具体字段显示</h4>
                    <p>名称：{$debug_data.name}</p>
                    <p>时间：{$debug_data.time|datetime}</p>
                    <p>数组：{$debug_data.array|implode=', '}</p>
                    
                    <h4>3. 调试按钮</h4>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="testAjax()">测试Ajax调试</button>
                        <button type="button" class="btn btn-info" onclick="testDatabase()">测试数据库调试</button>
                        <button type="button" class="btn btn-warning" onclick="testPerformance()">测试性能调试</button>
                        <button type="button" class="btn btn-danger" onclick="testError()">测试错误调试</button>
                    </div>
                    
                    <div id="debug-result" class="mt-3">
                        <h4>调试结果：</h4>
                        <pre id="result-content"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testAjax() {
    $.ajax({
        url: '{:url("debug/ajax")}',
        type: 'POST',
        data: {test: 'ajax调试', time: new Date().getTime()},
        success: function(response) {
            $('#result-content').html(JSON.stringify(response, null, 2));
        },
        error: function(xhr, status, error) {
            $('#result-content').html('错误：' + error);
        }
    });
}

function testDatabase() {
    $.ajax({
        url: '{:url("debug/database")}',
        type: 'GET',
        success: function(response) {
            $('#result-content').html(JSON.stringify(response, null, 2));
        },
        error: function(xhr, status, error) {
            $('#result-content').html('错误：' + error);
        }
    });
}

function testPerformance() {
    $.ajax({
        url: '{:url("debug/performance")}',
        type: 'GET',
        success: function(response) {
            $('#result-content').html(JSON.stringify(response, null, 2));
        },
        error: function(xhr, status, error) {
            $('#result-content').html('错误：' + error);
        }
    });
}

function testError() {
    $.ajax({
        url: '{:url("debug/error")}',
        type: 'GET',
        success: function(response) {
            $('#result-content').html(JSON.stringify(response, null, 2));
        },
        error: function(xhr, status, error) {
            $('#result-content').html('错误：' + error);
        }
    });
}
</script>

{include file="common/footer" /} 
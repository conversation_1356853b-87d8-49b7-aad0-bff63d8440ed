<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>《般若波罗蜜多心经》全文 - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
            color: #2c1810;
            margin: 0;
            padding: 0;
        }
        
        /* 内容容器 */
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 文章头部 */
        .article-header {
            background: white;
            border-radius: 12px;
            padding: 40px 30px;
            margin: 25px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .article-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c1810;
            margin-bottom: 20px;
            line-height: 1.3;
            text-align: center;
        }
        
        .article-meta {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: #8b4513;
            gap: 20px;
        }
        
        .expert-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #e9ecef;
            object-fit: cover;
        }
        
        .expert-info {
            text-align: center;
        }
        
        .expert-name {
            color: #8b4513;
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .expert-title {
            color: #a67b5b;
            font-size: 0.9rem;
        }
        
        .article-date {
            color: #8b4513;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c1810;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #8b4513;
            padding-bottom: 10px;
        }
        
        /* 主要内容区域 */
        .main-content-area {
            display: flex;
            gap: 20px;
            margin: 30px 0;
        }
        

        
        /* 左侧文章内容区域 */
        .article-content-area {
            flex: 1;
            min-width: 0;
        }
        
        /* 文章内容 */
        .article-content {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            line-height: 1.8;
            font-size: 1.1rem;
            color: #2c1810;
        }
        
        /* 右侧专家批注侧边栏 */
        .annotation-sidebar {
            width: 350px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }
        
        .annotation-sidebar.collapsed {
            width: 50px;
        }
        
        .annotation-sidebar-header {
            background: linear-gradient(135deg, #8b4513, #a67b5b);
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .annotation-sidebar-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .annotation-sidebar-toggle {
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .annotation-sidebar-toggle:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .annotation-sidebar.collapsed .annotation-sidebar-header h3 {
            display: none;
        }
        
        .annotation-sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 600px;
        }
        
        .annotation-sidebar.collapsed .annotation-sidebar-content {
            display: none;
        }
        
        /* 批注占位符 */
        .annotation-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 40px 20px;
        }
        
        .annotation-placeholder i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #dee2e6;
        }
        
        .annotation-placeholder p {
            margin: 0;
            font-size: 0.9rem;
        }
        
        /* 侧边栏批注样式 */
        .sidebar-annotations {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .sidebar-annotation-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        
        .sidebar-annotation-item.expert-1 {
            border-left-color: #ff6b6b;
        }
        
        .sidebar-annotation-item.expert-2 {
            border-left-color: #4ecdc4;
        }
        
        .sidebar-annotation-item.expert-3 {
            border-left-color: #45b7d1;
        }
        
        .sidebar-annotation-item.expert-4 {
            border-left-color: #96ceb4;
        }
        
        .sidebar-annotation-item.expert-5 {
            border-left-color: #feca57;
        }
        
        .sidebar-annotation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .sidebar-annotation-author {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .sidebar-annotation-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #e9ecef;
        }
        
        .sidebar-annotation-author-name {
            font-weight: 600;
            color: #8b4513;
            font-size: 0.9rem;
        }
        
        .sidebar-annotation-author-title {
            color: #a67b5b;
            font-size: 0.8rem;
        }
        
        .sidebar-annotation-type {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
        }
        
        .sidebar-annotation-type.add {
            background: #d4edda;
            color: #155724;
        }
        
        .sidebar-annotation-type.modify {
            background: #fff3cd;
            color: #856404;
        }
        
        .sidebar-annotation-type.delete {
            background: #f8d7da;
            color: #721c24;
        }
        
        .sidebar-annotation-content {
            color: #2c1810;
            line-height: 1.5;
            font-size: 0.9rem;
            margin-bottom: 12px;
        }
        
        .sidebar-annotation-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .sidebar-annotation-btn {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        
        .sidebar-annotation-btn:hover {
            background: #e9ecef;
        }
        
        .article-content h2 {
            color: #8b4513;
            font-weight: 600;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .article-content h3 {
            color: #a67b5b;
            font-weight: 600;
            margin-top: 25px;
            margin-bottom: 12px;
            font-size: 1.3rem;
        }
        
        .article-content p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        /* 专家批注 */
        .annotation-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        /* 批注组样式 */
        .annotation-group {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .annotation-group-header {
            background: linear-gradient(135deg, #8b4513, #a67b5b);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .annotation-group-title {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .annotation-group-count {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .annotation-item {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .annotation-item:last-child {
            border-bottom: none;
        }
        
        /* 专家颜色标识 */
        .annotation-item.expert-1 {
            border-left: 4px solid #ff6b6b;
        }
        
        .annotation-item.expert-2 {
            border-left: 4px solid #4ecdc4;
        }
        
        .annotation-item.expert-3 {
            border-left: 4px solid #45b7d1;
        }
        
        .annotation-item.expert-4 {
            border-left: 4px solid #96ceb4;
        }
        
        .annotation-item.expert-5 {
            border-left: 4px solid #feca57;
        }
        
        .annotation-item:hover {
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.1);
        }
        
        .annotation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .annotation-author {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .annotation-author img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #e9ecef;
        }
        
        .annotation-author-info {
            display: flex;
            flex-direction: column;
        }
        
        .annotation-author-name {
            font-weight: 600;
            color: #8b4513;
            font-size: 1rem;
        }
        
        .annotation-author-title {
            color: #a67b5b;
            font-size: 0.8rem;
        }
        
        .annotation-type {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .annotation-type.add {
            background: #d4edda;
            color: #155724;
        }
        
        .annotation-type.modify {
            background: #fff3cd;
            color: #856404;
        }
        
        .annotation-type.delete {
            background: #f8d7da;
            color: #721c24;
        }
        
        .annotation-content {
            color: #2c1810;
            line-height: 1.6;
            margin-bottom: 12px;
            font-size: 1rem;
        }
        
        .annotation-position {
            color: #8b4513;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        /* 批注投票区域 */
        .annotation-vote {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .annotation-vote-title {
            font-size: 0.9rem;
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .annotation-vote-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .annotation-vote-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }
        
        .annotation-vote-btn.support {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        
        .annotation-vote-btn.support:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
        
        .annotation-vote-btn.question {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }
        
        .annotation-vote-btn.question:hover {
            background: linear-gradient(135deg, #e0a800, #e8590c);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
        }
        
        .annotation-vote-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .annotation-vote-stats {
            color: #8b4513;
            font-size: 0.8rem;
        }
        
        /* 投票进度条 */
        .vote-progress {
            margin-top: 12px;
        }
        
        .vote-progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .vote-progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .vote-progress-fill.support {
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        
        .vote-progress-fill.question {
            background: linear-gradient(90deg, #ffc107, #fd7e14);
        }
        
        .vote-progress-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #8b4513;
        }
        
        .vote-progress-label {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .vote-progress-label.support {
            color: #28a745;
        }
        
        .vote-progress-label.question {
            color: #ffc107;
        }
        
        /* 批注评论区 */
        .annotation-comments {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .annotation-comment-form {
            margin-bottom: 15px;
        }
        
        .annotation-comment-textarea {
            border-radius: 6px;
            border: 2px solid #e9ecef;
            padding: 10px;
            resize: vertical;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            width: 100%;
            min-height: 60px;
        }
        
        .annotation-comment-textarea:focus {
            border-color: #8b4513;
            box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25);
            outline: none;
        }
        
        .annotation-comment-submit {
            background: linear-gradient(135deg, #8b4513, #a67b5b);
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-weight: 600;
            color: white;
            box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
            transition: all 0.3s ease;
            font-size: 0.9rem;
            margin-top: 8px;
        }
        
        .annotation-comment-submit:hover {
            background: linear-gradient(135deg, #a67b5b, #cd853f);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }
        
        .annotation-comment-item {
            border-bottom: 1px solid #e9ecef;
            padding: 12px 0;
        }
        
        .annotation-comment-item:last-child {
            border-bottom: none;
        }
        
        .annotation-comment-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 8px;
        }
        
        .annotation-comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #e9ecef;
            object-fit: cover;
        }
        
        .annotation-comment-author {
            font-weight: 600;
            color: #8b4513;
            font-size: 0.9rem;
        }
        
        .annotation-comment-date {
            color: #a67b5b;
            font-size: 0.8rem;
        }
        
        .annotation-comment-content {
            color: #2c1810;
            line-height: 1.5;
            font-size: 0.9rem;
        }
        
        /* 批注类型图例 */
        .annotation-legend {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .legend-items {
            display: flex;
            gap: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .legend-marker {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .legend-marker.add {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }
        
        .legend-marker.modify {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }
        
        .legend-marker.delete {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }
        
        .legend-text {
            font-size: 0.9rem;
            color: #2c1810;
            font-weight: 500;
        }
        
        /* 二维码区域 */
        .qr-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            background: #f8f9fa;
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #8b4513;
            transition: all 0.3s ease;
        }
        
        .qr-code:hover {
            border-color: #8b4513;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.1);
        }
        
        .qr-code i {
            font-size: 80px;
            margin-bottom: 10px;
            color: #a67b5b;
        }
        
        .qr-text {
            font-size: 0.9rem;
            color: #8b4513;
        }
        
        .qr-tip {
            color: #a67b5b;
            font-size: 0.9rem;
            margin-top: 15px;
        }
        

        
        /* 智能批注系统 */
        .smart-annotation-mode {
            position: relative;
        }
        
        /* 专家颜色标识 */
        .expert-colors {
            --expert-1: #ff6b6b;
            --expert-2: #4ecdc4;
            --expert-3: #45b7d1;
            --expert-4: #96ceb4;
            --expert-5: #feca57;
        }
        
        .annotation-highlight {
            padding: 2px 4px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        
        .annotation-highlight:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transform: translateY(-1px);
        }
        
        /* 不同专家的颜色 - 只保留背景色 */
        .annotation-highlight.expert-1 {
            background: linear-gradient(120deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 142, 142, 0.05) 100%);
        }
        
        .annotation-highlight.expert-2 {
            background: linear-gradient(120deg, rgba(78, 205, 196, 0.1) 0%, rgba(109, 213, 206, 0.05) 100%);
        }
        
        .annotation-highlight.expert-3 {
            background: linear-gradient(120deg, rgba(69, 183, 209, 0.1) 0%, rgba(103, 197, 219, 0.05) 100%);
        }
        
        .annotation-highlight.expert-4 {
            background: linear-gradient(120deg, rgba(150, 206, 180, 0.1) 0%, rgba(168, 213, 192, 0.05) 100%);
        }
        
        .annotation-highlight.expert-5 {
            background: linear-gradient(120deg, rgba(254, 202, 87, 0.1) 0%, rgba(255, 214, 122, 0.05) 100%);
        }
        
        /* 批注类型标识 */
        .annotation-highlight.add {
            background: rgba(40, 167, 69, 0.2);
        }
        
        .annotation-highlight.modify {
            background: rgba(255, 193, 7, 0.2);
        }
        
        .annotation-highlight.delete {
            background: rgba(220, 53, 69, 0.2);
        }
        

        
        /* 专家批注工具栏 */
        .expert-toolbar {
            position: absolute;
            background: white;
            border-radius: 4px;
            padding: 2px 4px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
            border: 1px solid #e9ecef;
            z-index: 1000;
            display: none;
            flex-direction: row;
            align-items: center;
            gap: 3px;
            opacity: 0;
            visibility: hidden;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .expert-toolbar.show {
            display: flex !important;
            flex-direction: row !important;
            align-items: center;
            gap: 3px;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .expert-toolbar-btn {
            padding: 2px;
            border: none;
            border-radius: 2px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            color: #6c757d;
            width: 28px;
            height: 28px;
            position: relative;
        }
        
        .expert-toolbar-btn:hover {
            background: #f8f9fa;
            color: #495057;
            transform: translateY(-1px);
        }
        
        .expert-toolbar-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        /* 工具提示样式 */
        .expert-toolbar-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #495057;
            color: white;
            padding: 3px 6px;
            border-radius: 2px;
            font-size: 0.85rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
            z-index: 1001;
        }
        
        .expert-toolbar-btn:hover::after {
            opacity: 1;
            visibility: visible;
        }
        
        .expert-toolbar-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .expert-toolbar-separator {
            width: 1px;
            height: 20px;
            background: #dee2e6;
            margin: 0 4px;
        }
        
        /* 专家身份标识 */
        .expert-badge {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 8px;
        }
        
        /* 登录提示 */
        .login-prompt {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: #1976d2;
            margin-bottom: 20px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-container {
                padding: 0 15px;
            }
            
            .article-header {
                padding: 30px 20px;
                margin: 20px 0;
            }
            
            .article-title {
                font-size: 2rem;
            }
            
            .article-meta {
                flex-direction: column;
                gap: 15px;
            }
            
            .vote-buttons {
                flex-direction: column;
                gap: 15px;
            }
            
            .vote-btn {
                width: 100%;
                justify-content: center;
            }
            
            .article-content {
                padding: 30px 20px;
            }
            
            .annotation-section,
            .qr-section,
            .comment-section {
                padding: 25px 20px;
            }
            
            .annotation-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .annotation-vote-buttons {
                flex-direction: column;
                gap: 8px;
            }
            
            .annotation-vote-btn {
                width: 100%;
                justify-content: center;
            }
            
            .vote-progress-labels {
                flex-direction: column;
                gap: 4px;
            }
            
            .expert-editor-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .expert-editor-toggle {
                width: 100%;
                justify-content: center;
            }
            
            .expert-form-actions {
                flex-direction: column;
            }
            
            .expert-form-submit,
            .expert-form-cancel {
                width: 100%;
                justify-content: center;
            }
            
            .expert-toolbar {
                position: fixed !important;
                top: 10px !important;
                right: 10px;
                left: 10px;
                width: auto;
                flex-direction: row !important;
                flex-wrap: wrap;
                gap: 4px;
                transform: none !important;
            }
            
            .expert-toolbar-btn {
                padding: 2px;
                font-size: 1rem;
                width: 26px;
                height: 26px;
            }
            

            
            .legend-items {
                flex-direction: column;
                gap: 15px;
            }
            
            .legend-item {
                width: 100%;
                justify-content: center;
            }
            
            .annotation-popup {
                position: fixed;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 350px;
            }
            
            .qr-code {
                width: 160px;
                height: 160px;
            }
            
            .qr-code i {
                font-size: 60px;
            }
        }
        
        @media (max-width: 480px) {
            .article-header {
                padding: 25px 15px;
            }
            
            .article-title {
                font-size: 1.6rem;
            }
            
            .expert-avatar {
                width: 50px;
                height: 50px;
            }
            
            .article-content {
                padding: 25px 15px;
                font-size: 1rem;
            }
            
            .annotation-section,
            .qr-section,
            .comment-section {
                padding: 20px 15px;
            }
            
            .qr-code {
                width: 140px;
                height: 140px;
            }
            
            .qr-code i {
                font-size: 50px;
            }
            
            /* 移动端批注对话框 */
            .annotation-type-dialog-content,
            .annotation-content-dialog-content {
                width: 95%;
                max-width: none;
                margin: 10px;
            }
            
            .annotation-type-options {
                flex-direction: column;
            }
            
            .annotation-content-actions {
                flex-direction: column;
            }
            
            .annotation-content-btn {
                width: 100%;
            }
            

            
            /* 移动端侧边栏 */
            .main-content-area {
                flex-direction: column;
            }
            
            .annotation-sidebar {
                width: 100%;
                margin-top: 20px;
            }
            
            .annotation-sidebar.collapsed {
                width: 100%;
                height: 50px;
            }
            
            .annotation-sidebar.collapsed .annotation-sidebar-content {
                display: none;
            }
        }
    </style>
</head>

<body>
    <!-- 引入公共头部导航 -->
    {include file="common/header" /}

    <!-- 内容区域 -->
    <div class="content-container">
        <!-- 文章头部 -->
        <section class="article-header">
            <h1 class="article-title">《般若波罗蜜多心经》全文</h1>
            
            <div class="article-meta">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=120&fit=crop&crop=face" class="expert-avatar" alt="专家头像">
                <div class="expert-info">
                    <div class="expert-name">释慧明法师</div>
                    <div class="expert-title">佛学专家 | 禅宗大师</div>
                </div>
                <div class="article-date">
                    <i class="fa fa-calendar"></i> 2024-01-15
                </div>
            </div>
        </section>

        <!-- 批注类型图例 -->
        <section class="annotation-legend">
            <h3 class="section-title">批注类型说明</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <span class="legend-marker add">绿色背景</span>
                    <span class="legend-text">新增内容：专家建议添加的内容</span>
                </div>
                <div class="legend-item">
                    <span class="legend-marker modify">黄色背景</span>
                    <span class="legend-text">修改内容：专家建议修改的内容</span>
                </div>
                <div class="legend-item">
                    <span class="legend-marker delete">红色背景</span>
                    <span class="legend-text">删除内容：专家建议删除的内容</span>
                </div>
            </div>
        </section>

        <!-- 主要内容区域 -->
        <div class="main-content-area">
            <!-- 左侧文章内容 -->
            <div class="article-content-area">
                <section class="article-content smart-annotation-mode">
                    <h2>般若波罗蜜多心经</h2>
                    <p><span class="annotation-highlight add expert-1" data-annotation="1" onclick="showSidebarAnnotation(this, 'add')">观自在菩萨</span>，行深般若波罗蜜多时，照见五蕴皆空，度一切苦厄。舍利子，色不异空，空不异色，色即是空，空即是色，受想行识，亦复如是。</p>
                    
                    <p>舍利子，是诸法空相，不生不灭，不垢不净，<span class="annotation-highlight modify expert-1" data-annotation="6" onclick="showSidebarAnnotation(this, 'modify')">不减。是故空中无色</span>，无受想行识，无眼耳鼻舌身意，无色声香味触法，无眼界，乃至无意识界，无无明，亦无无明尽，乃至无老死，亦无老死尽。无苦集灭道，无智亦无得。</p>
                    
                    <p>舍利子，是诸法空相，不生不灭，不垢不净，不增不减。是故<span class="annotation-highlight add expert-2" data-annotation="8" onclick="showSidebarAnnotation(this, 'add')">空中无色</span>，无受想行识，无眼耳鼻舌身意，无色声香味触法，无眼界，乃至无意识界，无无明，亦无无明尽，乃至无老死，亦无老死尽。无苦集灭道，无智亦无得。</p>
                    
                    <p>以无所得故，菩提萨埵，依般若波罗蜜多故，心无挂碍。无挂碍故，无有恐怖，远离颠倒梦想，<span class="annotation-highlight add expert-2" data-annotation="7" onclick="showSidebarAnnotation(this, 'add')">究竟涅槃</span>。三世诸佛，依般若波罗蜜多故，得阿耨多罗三藐三菩提。</p>
                    
                    <p>故知般若波罗蜜多，是大神咒，是大明咒，是无上咒，是<span class="annotation-highlight add expert-3" data-annotation="3" onclick="showSidebarAnnotation(this, 'add')">无等等咒</span>，能除一切苦，真实不虚。故说般若波罗蜜多咒，即说咒曰：揭谛揭谛，波罗揭谛，波罗僧揭谛，菩提萨婆诃。</p>
                    
                    <p>故知般若波罗蜜多，是大神咒，是大明咒，是无上咒，是无<span class="annotation-highlight modify expert-4" data-annotation="4" onclick="showSidebarAnnotation(this, 'modify')">等等</span>咒，能除一切苦，真实不虚。故说般若波罗蜜多咒，即说咒曰：揭谛揭谛，波罗揭谛，波罗僧揭谛，菩提萨婆诃。</p>
                    
                    <h2>经文释义</h2>
                    <h3>一、观自在菩萨</h3>
                    <p>观自在菩萨即观世音菩萨，以慈悲心观照世间众生，寻声救苦，故名观世音。菩萨行深般若波罗蜜多时，即是以甚深智慧观照诸法实相。</p>
                    
                    <h3>二、五蕴皆空</h3>
                    <p>五蕴即色、受、想、行、识。色蕴指物质世界，受蕴指感受，想蕴指思维，行蕴指意志活动，识蕴指认识作用。<span class="annotation-highlight modify expert-2" data-annotation="2" onclick="showSidebarAnnotation(this, 'modify')">五蕴皆空</span>，即是说一切现象都是因缘和合而生，没有独立的自性。</p>
                    
                    <h3>三、诸法空相</h3>
                    <p>诸法空相，不生不灭，不垢不净，不增不减。这是说明诸法的真实相状，即空性。空性不是虚无，而是超越有无的相对概念。</p>
                    
                    <h2>修行指导</h2>
                    <h3>一、观照般若</h3>
                    <p>修行者应当以般若智慧观照一切法，了知诸法如幻如化，无有实性。通过观照，能够破除执着，获得解脱。</p>
                    
                    <h3>二、心无挂碍</h3>
                    <p>依般若波罗蜜多，心无挂碍。无挂碍故，无有恐怖，远离颠倒梦想，究竟涅槃。这是说明般若智慧的作用。</p>
                    
                    <h3>三、究竟涅槃</h3>
                    <p>通过般若智慧的观照，能够达到究竟涅槃的境界，即彻底解脱生死轮回，获得永恒的安乐。</p>
                </section>
            </div>

            <!-- 右侧专家批注区域 -->
            <div class="annotation-sidebar" id="annotationSidebar">
                <div class="annotation-sidebar-header">
                    <h3>专家批注</h3>
                    <div class="annotation-sidebar-toggle" onclick="toggleAnnotationSidebar()">
                        <i class="fa fa-chevron-left"></i>
                    </div>
                </div>
                <div class="annotation-sidebar-content" id="annotationSidebarContent">
                    <div class="annotation-placeholder">
                        <i class="fa fa-comments"></i>
                        <p>选择文本查看专家批注</p>
                    </div>
                </div>
            </div>
        </div>





        <!-- 专家批注工具栏 -->
        <div class="expert-toolbar" id="expertToolbar">
            <button class="expert-toolbar-btn add" onclick="createAnnotation('add')" id="addBtn" data-tooltip="新增">
                <i class="fa fa-plus"></i>
            </button>
            <button class="expert-toolbar-btn modify disabled" onclick="createAnnotation('modify')" id="modifyBtn" data-tooltip="修改">
                <i class="fa fa-edit"></i>
            </button>
            <button class="expert-toolbar-btn delete disabled" onclick="createAnnotation('delete')" id="deleteBtn" data-tooltip="删除">
                <i class="fa fa-trash"></i>
            </button>
        </div>





        <!-- 入群二维码 -->
        <section class="qr-section">
            <h3 class="section-title">加入讨论群</h3>
            <p class="qr-tip">扫描下方二维码，加入微信群，与专家和其他读者一起讨论本文内容</p>
            
            <div class="qr-code">
                <i class="fa fa-qrcode"></i>
                <div class="qr-text">微信群二维码</div>
            </div>
            
            <p class="qr-tip">
                <i class="fa fa-info-circle"></i> 
                扫码加入佛经学习交流群
            </p>
        </section>


    </div>

    <!-- 引入公共页脚 -->
    {include file="common/footer" /}

    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
    <script>
        // 批注投票功能
        function voteAnnotation(annotationId, type, expertId) {
            // 这里可以添加批注投票逻辑
            if (typeof layer !== 'undefined') {
                layer.msg('投票成功！感谢您的参与。', {icon: 1});
            } else {
                alert('投票成功！感谢您的参与。');
            }
            
            // 更新按钮状态
            const voteButtons = $(`.annotation-item:nth-child(${annotationId}) .annotation-vote-btn`);
            if (type === 'support') {
                voteButtons.filter('.support').addClass('active').prop('disabled', true);
                voteButtons.filter('.question').prop('disabled', true);
            } else {
                voteButtons.filter('.question').addClass('active').prop('disabled', true);
                voteButtons.filter('.support').prop('disabled', true);
            }
            
            // 更新进度条
            updateVoteProgress(annotationId, type, expertId);
        }
        
        // 更新投票进度条
        function updateVoteProgress(annotationId, type, expertId) {
            const annotationItem = $(`.annotation-item:nth-child(${annotationId})`);
            const progressFill = annotationItem.find('.vote-progress-fill');
            const voteLabels = annotationItem.find('.vote-progress-labels .vote-progress-label');
            const voteStats = annotationItem.find('.annotation-vote-stats');
            
            // 获取当前投票数据
            let supportCount = 15;
            let questionCount = 5;
            
            // 根据投票类型更新数据
            if (type === 'support') {
                supportCount++;
            } else {
                questionCount++;
            }
            
            const totalCount = supportCount + questionCount;
            const supportPercent = Math.round((supportCount / totalCount) * 100);
            const questionPercent = 100 - supportPercent;
            
            // 更新进度条
            progressFill.css('width', supportPercent + '%');
            
            // 更新标签文本
            if (voteLabels.length >= 2) {
                voteLabels.eq(0).find('.vote-count').text(`${supportCount}票 (${supportPercent}%)`);
                voteLabels.eq(1).find('.vote-count').text(`${questionCount}票 (${questionPercent}%)`);
                voteStats.find('.vote-total').text(`总计：${totalCount}票`);
            }
        }
        
        // 批注评论提交功能
        function submitAnnotationComment(event, annotationId, expertId) {
            event.preventDefault();
            const button = event.target;
            const commentForm = button.closest('.annotation-comments');
            const textarea = commentForm.querySelector('.annotation-comment-textarea');
            const content = textarea.value.trim();
            
            if (!content) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请输入评论内容', {icon: 2});
                } else {
                    alert('请输入评论内容');
                }
                return;
            }
            
            // 这里可以添加提交批注评论的逻辑
            if (typeof layer !== 'undefined') {
                layer.msg('评论发表成功！', {icon: 1});
            } else {
                alert('评论发表成功！');
            }
            
            // 清空文本框
            textarea.value = '';
        }
        
        // 智能批注系统
        let currentAnnotationType = null;
        let isAnnotationMode = false;
        let isExpertUser = true; // 模拟专家用户状态
        let currentSelectionRange = null; // 保存当前选中的文本范围
        
        // 模拟多专家批注数据
        var multiExpertAnnotations = {
            '1': {
                experts: [
                    {
                        id: 1,
                        name: '张教授',
                        title: '佛学专家 | 北京大学',
                        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-1',
                        type: 'add',
                        content: '观自在菩萨是观世音菩萨的别称，以慈悲心观照世间众生，寻声救苦。'
                    }
                ]
            },
            '2': {
                experts: [
                    {
                        id: 2,
                        name: '李博士',
                        title: '哲学专家 | 清华大学',
                        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-2',
                        type: 'modify',
                        content: '五蕴皆空是佛教核心教义，指色、受、想、行、识五蕴都没有独立的自性。'
                    }
                ]
            },
            '3': {
                experts: [
                    {
                        id: 3,
                        name: '王教授',
                        title: '佛学专家 | 复旦大学',
                        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-3',
                        type: 'add',
                        content: '无等等咒是般若波罗蜜多心经中的重要概念，表示无上、无等的咒语。'
                    }
                ]
            },
            '4': {
                experts: [
                    {
                        id: 4,
                        name: '陈博士',
                        title: '佛学专家 | 南京大学',
                        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-4',
                        type: 'modify',
                        content: '等等表示平等、无差别，强调般若智慧的普遍性和平等性。'
                    }
                ]
            },
            '5': {
                experts: [
                    {
                        id: 5,
                        name: '刘教授',
                        title: '佛学专家 | 中国社会科学院',
                        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-5',
                        type: 'add',
                        content: '空中无色是般若心经的核心教义，指在空性中没有任何固定的形相，一切法都是因缘和合而生，没有独立的自性。'
                    }
                ]
            },
            '6': {
                experts: [
                    {
                        id: 6,
                        name: '张教授',
                        title: '佛学专家 | 北京大学',
                        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-1',
                        type: 'modify',
                        content: '不减。是故空中无色 - 这是般若心经的核心教义，指在空性中没有任何固定的形相，一切法都是因缘和合而生，没有独立的自性。'
                    }
                ]
            },
            '7': {
                experts: [
                    {
                        id: 7,
                        name: '孙教授',
                        title: '佛学专家 | 浙江大学',
                        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-2',
                        type: 'add',
                        content: '究竟涅槃是佛教修行的最终目标，指彻底解脱生死轮回，获得永恒的安乐和智慧。'
                    }
                ]
            },
            '8': {
                experts: [
                    {
                        id: 8,
                        name: '李博士',
                        title: '佛学专家 | 清华大学',
                        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-2',
                        type: 'add',
                        content: '是故空 - 这是般若心经中的重要转折，指因此缘故，一切法都是空性，没有固定的自性。'
                    }
                ]
            }
        };
        
        // 检查文本是否已经被批注过
        function checkIfAlreadyAnnotated(range) {
            // 获取选中文本的起始和结束节点
            const startContainer = range.startContainer;
            const endContainer = range.endContainer;
            
            // 检查起始节点是否在批注元素内
            let startNode = startContainer;
            while (startNode && startNode !== document.body) {
                if (startNode.nodeType === Node.ELEMENT_NODE && 
                    startNode.classList && 
                    startNode.classList.contains('annotation-highlight')) {
                    return true;
                }
                startNode = startNode.parentNode;
            }
            
            // 检查结束节点是否在批注元素内
            let endNode = endContainer;
            while (endNode && endNode !== document.body) {
                if (endNode.nodeType === Node.ELEMENT_NODE && 
                    endNode.classList && 
                    endNode.classList.contains('annotation-highlight')) {
                    return true;
                }
                endNode = endNode.parentNode;
            }
            
            // 检查选中范围内的所有节点
            const walker = document.createTreeWalker(
                range.commonAncestorContainer,
                NodeFilter.SHOW_ELEMENT,
                null,
                false
            );
            
            let node;
            while (node = walker.nextNode()) {
                if (node.classList && node.classList.contains('annotation-highlight')) {
                    // 检查这个批注元素是否与选中的范围有重叠
                    const nodeRange = document.createRange();
                    nodeRange.selectNode(node);
                    
                    if (range.compareBoundaryPoints(Range.START_TO_END, nodeRange) <= 0 &&
                        range.compareBoundaryPoints(Range.END_TO_START, nodeRange) >= 0) {
                        return true;
                    }
                }
            }
            
            return false;
        }
        
        // 更新工具栏位置的函数
        function updateToolbarPosition() {
            const toolbar = document.getElementById('expertToolbar');
            if (toolbar && toolbar.classList.contains('show')) {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const rect = range.getBoundingClientRect();
                    
                    // 更新工具栏位置
                    toolbar.style.position = 'fixed';
                    toolbar.style.top = (rect.top - 45) + 'px';
                    toolbar.style.left = (rect.left + rect.width / 2) + 'px';
                    toolbar.style.transform = 'translateX(-50%)';
                }
            }
        }
        
        // 显示专家工具栏
        function showExpertToolbar() {
            const toolbar = document.getElementById('expertToolbar');
            toolbar.classList.add('show');
            updateToolbarPosition();
        }
        
        // 隐藏专家工具栏
        function hideExpertToolbar() {
            const toolbar = document.getElementById('expertToolbar');
            toolbar.classList.remove('show');
        }
        
        // 启用所有按钮
        function enableAllButtons() {
            const addBtn = document.getElementById('addBtn');
            const modifyBtn = document.getElementById('modifyBtn');
            const deleteBtn = document.getElementById('deleteBtn');
            
            addBtn.classList.remove('disabled');
            modifyBtn.classList.remove('disabled');
            deleteBtn.classList.remove('disabled');
        }
        

        
        // 隐藏专家工具栏
        function hideExpertToolbar() {
            const toolbar = document.getElementById('expertToolbar');
            toolbar.classList.remove('show');
        }
        
        // 智能文本选择处理
        function handleSmartTextSelection() {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            
            if (isExpertUser) {
                // 检查选中的文本是否在 article-content-area 内
                const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
                const articleContentArea = document.querySelector('.article-content-area');
                
                if (range && articleContentArea && articleContentArea.contains(range.commonAncestorContainer)) {
                    if (selectedText.length > 0) {
                        // 检查选中的文本是否已经被批注过
                        const isAlreadyAnnotated = checkIfAlreadyAnnotated(range);
                        
                        if (isAlreadyAnnotated) {
                            // 如果已经被批注过，清除选择并提示
                            selection.removeAllRanges();
                            if (typeof layer !== 'undefined') {
                                layer.msg('该文本已经被批注过，不能重复批注', {icon: 2});
                            } else {
                                alert('该文本已经被批注过，不能重复批注');
                            }
                            hideExpertToolbar();
                        } else {
                            // 有选中文本且未被批注，启用所有按钮
                            enableAllButtons();
                            // 保存当前选中的范围
                            currentSelectionRange = range.cloneRange();
                            // 显示工具栏
                            showExpertToolbar();
                        }
                    } else {
                        // 没有选中文本，隐藏工具栏
                        hideExpertToolbar();
                    }
                } else {
                    // 不在指定区域内，隐藏工具栏
                    hideExpertToolbar();
                }
            }
        }
        
        // 显示批注类型选择对话框
        function showAnnotationTypeDialog(selectedText) {
            const dialog = document.createElement('div');
            dialog.className = 'annotation-type-dialog';
            dialog.innerHTML = `
                <div class="annotation-type-dialog-content">
                    <div class="annotation-type-dialog-header">
                        <h3>选择批注类型</h3>
                        <button onclick="closeAnnotationTypeDialog()">×</button>
                    </div>
                    <div class="annotation-type-dialog-body">
                        <p>您选择了："${selectedText}"</p>
                        <div class="annotation-type-options">
                            <button class="annotation-type-btn add" onclick="createAnnotation('add', '${selectedText.replace(/'/g, "\\'")}')">
                                <i class="fa fa-plus"></i> 新增内容
                            </button>
                            <button class="annotation-type-btn modify" onclick="createAnnotation('modify', '${selectedText.replace(/'/g, "\\'")}')">
                                <i class="fa fa-edit"></i> 修改内容
                            </button>
                            <button class="annotation-type-btn delete" onclick="createAnnotation('delete', '${selectedText.replace(/'/g, "\\'")}')">
                                <i class="fa fa-trash"></i> 删除内容
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // 添加样式
            if (!document.getElementById('annotation-type-dialog-styles')) {
                const style = document.createElement('style');
                style.id = 'annotation-type-dialog-styles';
                style.textContent = `
                    .annotation-type-dialog {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                    }
                    
                    .annotation-type-dialog-content {
                        background: white;
                        border-radius: 12px;
                        padding: 20px;
                        max-width: 400px;
                        width: 90%;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    }
                    
                    .annotation-type-dialog-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 15px;
                        padding-bottom: 10px;
                        border-bottom: 1px solid #e9ecef;
                    }
                    
                    .annotation-type-dialog-header h3 {
                        margin: 0;
                        color: #8b4513;
                        font-size: 1.1rem;
                    }
                    
                    .annotation-type-dialog-header button {
                        background: none;
                        border: none;
                        font-size: 1.5rem;
                        color: #999;
                        cursor: pointer;
                    }
                    
                    .annotation-type-options {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                        margin-top: 15px;
                    }
                    
                    .annotation-type-btn {
                        padding: 12px 16px;
                        border: none;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                    
                    .annotation-type-btn.add {
                        background: #d4edda;
                        color: #155724;
                    }
                    
                    .annotation-type-btn.add:hover {
                        background: #c3e6cb;
                    }
                    
                    .annotation-type-btn.modify {
                        background: #fff3cd;
                        color: #856404;
                    }
                    
                    .annotation-type-btn.modify:hover {
                        background: #ffeaa7;
                    }
                    
                    .annotation-type-btn.delete {
                        background: #f8d7da;
                        color: #721c24;
                    }
                    
                    .annotation-type-btn.delete:hover {
                        background: #f5c6cb;
                    }
                `;
                document.head.appendChild(style);
            }
        }
        
        // 关闭批注类型对话框
        function closeAnnotationTypeDialog() {
            const dialog = document.querySelector('.annotation-type-dialog');
            if (dialog) {
                dialog.remove();
            }
        }
        
        // 创建批注
        function createAnnotation(type) {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            
            if (selectedText.length > 0) {
                showAnnotationContentDialog(type, selectedText);
            } else {
                if (typeof layer !== 'undefined') {
                    layer.msg('请先选择要批注的文本', {icon: 2});
                } else {
                    alert('请先选择要批注的文本');
                }
            }
        }
        
        // 显示批注内容输入对话框
        function showAnnotationContentDialog(type, text) {
            const dialog = document.createElement('div');
            dialog.className = 'annotation-content-dialog';
            dialog.innerHTML = `
                <div class="annotation-content-dialog-content">
                    <div class="annotation-content-dialog-header">
                        <h3>${getTypeText(type)}批注</h3>
                        <button onclick="closeAnnotationContentDialog()">×</button>
                    </div>
                    <div class="annotation-content-dialog-body">
                        <div class="annotation-content-info">
                            <p><strong>选中的文本：</strong>"${text}"</p>
                            <p><strong>批注类型：</strong><span class="annotation-type-badge ${type}">${getTypeText(type)}</span></p>
                        </div>
                        <div class="annotation-content-form">
                            <label for="annotation-content">请输入您的批注内容：</label>
                            <textarea id="annotation-content" placeholder="请详细说明您的批注内容..." rows="6"></textarea>
                            <div class="annotation-content-actions">
                                <button class="annotation-content-btn primary" onclick="submitAnnotationContent('${type}', '${text.replace(/'/g, "\\'")}')">
                                    <i class="fa fa-check"></i> 提交批注
                                </button>
                                <button class="annotation-content-btn secondary" onclick="closeAnnotationContentDialog()">
                                    <i class="fa fa-times"></i> 取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // 添加样式
            if (!document.getElementById('annotation-content-dialog-styles')) {
                const style = document.createElement('style');
                style.id = 'annotation-content-dialog-styles';
                style.textContent = `
                    .annotation-content-dialog {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                    }
                    
                    .annotation-content-dialog-content {
                        background: white;
                        border-radius: 12px;
                        padding: 0;
                        max-width: 600px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    }
                    
                    .annotation-content-dialog-header {
                        background: linear-gradient(135deg, #8b4513, #a67b5b);
                        color: white;
                        padding: 20px;
                        border-radius: 12px 12px 0 0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .annotation-content-dialog-header h3 {
                        margin: 0;
                        font-size: 1.2rem;
                        font-weight: 600;
                    }
                    
                    .annotation-content-dialog-header button {
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.5rem;
                        cursor: pointer;
                        padding: 0;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .annotation-content-dialog-body {
                        padding: 20px;
                    }
                    
                    .annotation-content-info {
                        background: #f8f9fa;
                        border-radius: 8px;
                        padding: 15px;
                        margin-bottom: 20px;
                        border-left: 4px solid #8b4513;
                    }
                    
                    .annotation-content-info p {
                        margin: 0 0 8px 0;
                        color: #2c1810;
                    }
                    
                    .annotation-content-info p:last-child {
                        margin-bottom: 0;
                    }
                    
                    .annotation-type-badge {
                        display: inline-block;
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-size: 0.8rem;
                        font-weight: 600;
                    }
                    
                    .annotation-type-badge.add {
                        background: #d4edda;
                        color: #155724;
                    }
                    
                    .annotation-type-badge.modify {
                        background: #fff3cd;
                        color: #856404;
                    }
                    
                    .annotation-type-badge.delete {
                        background: #f8d7da;
                        color: #721c24;
                    }
                    
                    .annotation-content-form label {
                        display: block;
                        margin-bottom: 8px;
                        font-weight: 600;
                        color: #2c1810;
                    }
                    
                    .annotation-content-form textarea {
                        width: 100%;
                        border: 2px solid #e9ecef;
                        border-radius: 8px;
                        padding: 12px;
                        font-size: 0.9rem;
                        line-height: 1.5;
                        resize: vertical;
                        font-family: inherit;
                    }
                    
                    .annotation-content-form textarea:focus {
                        outline: none;
                        border-color: #8b4513;
                        box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
                    }
                    
                    .annotation-content-actions {
                        display: flex;
                        gap: 10px;
                        margin-top: 15px;
                        justify-content: flex-end;
                    }
                    
                    .annotation-content-btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                    }
                    
                    .annotation-content-btn.primary {
                        background: #8b4513;
                        color: white;
                    }
                    
                    .annotation-content-btn.primary:hover {
                        background: #a67b5b;
                    }
                    
                    .annotation-content-btn.secondary {
                        background: #f8f9fa;
                        color: #6c757d;
                        border: 1px solid #dee2e6;
                    }
                    
                    .annotation-content-btn.secondary:hover {
                        background: #e9ecef;
                    }
                `;
                document.head.appendChild(style);
            }
        }
        
        // 关闭批注内容输入对话框
        function closeAnnotationContentDialog() {
            const dialog = document.querySelector('.annotation-content-dialog');
            if (dialog) {
                dialog.remove();
            }
            // 清除保存的范围
            currentSelectionRange = null;
        }
        
        // 提交批注内容
        function submitAnnotationContent(type, text) {
            const textarea = document.getElementById('annotation-content');
            const content = textarea.value.trim();
            
            if (!content) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请输入批注内容', {icon: 2});
                } else {
                    alert('请输入批注内容');
                }
                return;
            }
            
            // 使用保存的文本范围
            if (currentSelectionRange) {
                try {
                    // 分配专家颜色
                    const expertColors = ['expert-1', 'expert-2', 'expert-3', 'expert-4', 'expert-5'];
                    const randomColor = expertColors[Math.floor(Math.random() * expertColors.length)];
                    
                    // 所有批注类型都使用相同的方式：标记选中的文本
                    const span = document.createElement('span');
                    span.className = `annotation-highlight ${type} ${randomColor}`;
                    span.textContent = text;
                    span.setAttribute('data-annotation-content', content);
                    
                    span.onclick = function() {
                        showSidebarAnnotation(this, type);
                    };
                    
                    // 防止新创建的批注被选中
                    span.addEventListener('mousedown', function(e) {
                        e.preventDefault();
                        return false;
                    });
                    
                    // 使用保存的范围创建批注
                    currentSelectionRange.surroundContents(span);
                    
                    // 清除保存的范围
                    currentSelectionRange = null;
                    
                    // 关闭对话框
                    closeAnnotationContentDialog();
                    
                    // 隐藏工具栏
                    hideExpertToolbar();
                    
                    if (typeof layer !== 'undefined') {
                        layer.msg('批注已成功添加！', {icon: 1});
                    } else {
                        alert('批注已成功添加！');
                    }
                } catch (error) {
                    console.error('批注创建失败:', error);
                    
                    // 如果范围无效，尝试重新获取当前选择
                    try {
                        const selection = window.getSelection();
                        if (selection.rangeCount > 0) {
                            const newRange = selection.getRangeAt(0);
                            const newSpan = document.createElement('span');
                            newSpan.className = `annotation-highlight ${type} ${randomColor}`;
                            
                            // 所有批注类型都使用相同的方式：标记选中的文本
                            newSpan.textContent = text;
                            newSpan.setAttribute('data-annotation-content', content);
                            newRange.surroundContents(newSpan);
                            
                            newSpan.onclick = function() {
                                showSidebarAnnotation(this, type);
                            };
                            
                            // 防止新创建的批注被选中
                            newSpan.addEventListener('mousedown', function(e) {
                                e.preventDefault();
                                return false;
                            });
                            
                            // 清除保存的范围
                            currentSelectionRange = null;
                            
                            // 关闭对话框
                            closeAnnotationContentDialog();
                            
                            // 隐藏工具栏
                            hideExpertToolbar();
                            
                            if (typeof layer !== 'undefined') {
                                layer.msg('批注已成功添加！', {icon: 1});
                            } else {
                                alert('批注已成功添加！');
                            }
                        } else {
                            throw new Error('无法获取有效的文本范围');
                        }
                    } catch (retryError) {
                        console.error('重试批注创建也失败:', retryError);
                        if (typeof layer !== 'undefined') {
                            layer.msg('批注创建失败，请重新选择文本', {icon: 2});
                        } else {
                            alert('批注创建失败，请重新选择文本');
                        }
                    }
                }
            } else {
                if (typeof layer !== 'undefined') {
                    layer.msg('批注创建失败，请重新选择位置', {icon: 2});
                } else {
                    alert('批注创建失败，请重新选择位置');
                }
            }
        }
        
        // 显示侧边栏批注
        function showSidebarAnnotation(element, type) {
            const sidebar = document.getElementById('annotationSidebar');
            const content = document.getElementById('annotationSidebarContent');
            
            // 获取该位置的批注数据
            const annotationId = element.getAttribute('data-annotation') || Math.floor(Math.random() * 1000);
            const annotations = multiExpertAnnotations[annotationId] || { experts: [] };
            
            // 获取当前批注的内容
            const currentContent = element.getAttribute('data-annotation-content') || '这是专家对选中文本的批注内容。';
            
            // 添加当前专家的批注
            const currentExpert = {
                id: Date.now(),
                name: '当前专家',
                title: '专家用户',
                avatar: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=80&h=80&fit=crop&crop=face',
                color: element.className.includes('expert-1') ? 'expert-1' : 
                       element.className.includes('expert-2') ? 'expert-2' : 
                       element.className.includes('expert-3') ? 'expert-3' : 
                       element.className.includes('expert-4') ? 'expert-4' : 'expert-5',
                type: type,
                content: currentContent
            };
            
            // 合并所有专家的批注
            const allExperts = [currentExpert, ...annotations.experts];
            
            // 生成批注HTML
            let annotationsHtml = '';
            allExperts.forEach((expert, index) => {
                annotationsHtml += `
                    <div class="sidebar-annotation-item ${expert.color}">
                        <div class="sidebar-annotation-header">
                            <div class="sidebar-annotation-author">
                                <img src="${expert.avatar}" class="sidebar-annotation-avatar" alt="专家头像">
                                <div class="sidebar-annotation-author-info">
                                    <div class="sidebar-annotation-author-name">${expert.name}</div>
                                    <div class="sidebar-annotation-author-title">${expert.title}</div>
                                </div>
                            </div>
                            <span class="sidebar-annotation-type ${expert.type}">${getTypeText(expert.type)}</span>
                        </div>
                        <div class="sidebar-annotation-content">
                            ${expert.content}
                        </div>
                        <div class="sidebar-annotation-actions">
                            <button class="sidebar-annotation-btn" onclick="voteAnnotation(${annotationId}, 'support', ${expert.id})">
                                <i class="fa fa-thumbs-up"></i> 支持
                            </button>
                            <button class="sidebar-annotation-btn" onclick="voteAnnotation(${annotationId}, 'question', ${expert.id})">
                                <i class="fa fa-question-circle"></i> 疑问
                            </button>
                            <button class="sidebar-annotation-btn" onclick="addComment(${annotationId}, ${expert.id})">
                                <i class="fa fa-comment"></i> 评论
                            </button>
                        </div>
                    </div>
                `;
            });
            
            // 填充侧边栏内容
            content.innerHTML = `
                <div class="sidebar-annotations">
                    ${annotationsHtml}
                </div>
            `;
            
            // 显示侧边栏
            sidebar.classList.remove('collapsed');
        }
        
        // 切换侧边栏显示/隐藏
        function toggleAnnotationSidebar() {
            const sidebar = document.getElementById('annotationSidebar');
            sidebar.classList.toggle('collapsed');
        }
        
        // 添加评论
        function addComment(annotationId, expertId) {
            const commentText = prompt('请输入您的评论：');
            if (commentText && commentText.trim()) {
                if (typeof layer !== 'undefined') {
                    layer.msg('评论已添加！', {icon: 1});
                } else {
                    alert('评论已添加！');
                }
            }
        }
        
        // 切换批注标签页
        function switchAnnotationTab(index) {
            const tabs = document.querySelectorAll('.annotation-popup-tab');
            const contents = document.querySelectorAll('.annotation-popup-tab-content');
            
            tabs.forEach((tab, i) => {
                tab.classList.toggle('active', i === index);
            });
            
            contents.forEach((content, i) => {
                content.classList.toggle('active', i === index);
            });
        }
        
        // 滚动到批注区域
        function scrollToAnnotationSection(annotationId) {
            const annotationSection = document.querySelector('.annotation-section');
            if (annotationSection) {
                annotationSection.scrollIntoView({ behavior: 'smooth' });
                closeAnnotationPopup();
                
                // 高亮对应的批注项
                const annotationItems = document.querySelectorAll('.annotation-item');
                annotationItems.forEach(item => {
                    item.style.border = 'none';
                });
                
                // 这里可以根据annotationId找到对应的批注项并高亮
                if (typeof layer !== 'undefined') {
                    layer.msg('已定位到批注区域', {icon: 1});
                }
            }
        }
        

        
        // 获取批注类型文本
        function getTypeText(type) {
            const typeMap = {
                'add': '新增内容',
                'modify': '修改内容',
                'delete': '删除内容'
            };
            return typeMap[type] || '未知类型';
        }
        
        // 页面加载时设置文本选择事件
        document.addEventListener('DOMContentLoaded', function() {
            // 只在 article-content-area 内添加文本选择事件
            const articleContentArea = document.querySelector('.article-content-area');
            if (articleContentArea) {
                articleContentArea.addEventListener('mouseup', handleSmartTextSelection);
            }
            
            // 点击其他地方时隐藏工具栏
            document.addEventListener('click', function(event) {
                const toolbar = document.getElementById('expertToolbar');
                if (toolbar && !toolbar.contains(event.target) && !articleContentArea.contains(event.target)) {
                    hideExpertToolbar();
                }
            });
            

            
            // 滚动时更新工具栏位置
            document.addEventListener('scroll', function() {
                updateToolbarPosition();
            });
            
            // 窗口大小改变时更新工具栏位置
            window.addEventListener('resize', function() {
                updateToolbarPosition();
            });
            
            // 模拟专家用户登录状态
            if (isExpertUser) {
                // 不自动显示工具栏，只有在选中文本时才显示
                // setTimeout(() => {
                //     showExpertToolbar();
                // }, 1000);
            }
        });

        // 显示批注弹窗（已废弃，改为使用侧边栏）
        function showAnnotationPopup(element, annotationId) {
            // 直接调用侧边栏显示函数
            const type = element.className.includes('add') ? 'add' : 
                        element.className.includes('modify') ? 'modify' : 'delete';
            showSidebarAnnotation(element, type);
        }

        // 显示侧边栏批注
        function showSidebarAnnotation(element, type) {
            const sidebar = document.getElementById('annotationSidebar');
            const content = document.getElementById('annotationSidebarContent');
            
            // 获取该位置的批注数据
            const annotationId = element.getAttribute('data-annotation') || Math.floor(Math.random() * 1000);
            const annotations = multiExpertAnnotations[annotationId] || { experts: [] };
            
            // 获取当前批注的内容
            const currentContent = element.getAttribute('data-annotation-content') || '这是专家对选中文本的批注内容。';
            
            // 添加当前专家的批注
            const currentExpert = {
                id: Date.now(),
                name: '当前专家',
                title: '专家用户',
                avatar: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=80&h=80&fit=crop&crop=face',
                color: element.className.includes('expert-1') ? 'expert-1' : 
                       element.className.includes('expert-2') ? 'expert-2' : 
                       element.className.includes('expert-3') ? 'expert-3' : 
                       element.className.includes('expert-4') ? 'expert-4' : 'expert-5',
                type: type,
                content: currentContent
            };
            
            // 合并所有专家的批注
            const allExperts = [currentExpert, ...annotations.experts];
            
            // 生成批注HTML
            let annotationsHtml = '';
            allExperts.forEach((expert, index) => {
                annotationsHtml += `
                    <div class="sidebar-annotation-item ${expert.color}">
                        <div class="sidebar-annotation-header">
                            <div class="sidebar-annotation-author">
                                <img src="${expert.avatar}" class="sidebar-annotation-avatar" alt="专家头像">
                                <div class="sidebar-annotation-author-info">
                                    <div class="sidebar-annotation-author-name">${expert.name}</div>
                                    <div class="sidebar-annotation-author-title">${expert.title}</div>
                                </div>
                            </div>
                            <span class="sidebar-annotation-type ${expert.type}">${getTypeText(expert.type)}</span>
                        </div>
                        <div class="sidebar-annotation-content">
                            ${expert.content}
                        </div>
                        <div class="sidebar-annotation-actions">
                            <button class="sidebar-annotation-btn" onclick="voteAnnotation(${annotationId}, 'support', ${expert.id})">
                                <i class="fa fa-thumbs-up"></i> 支持
                            </button>
                            <button class="sidebar-annotation-btn" onclick="voteAnnotation(${annotationId}, 'question', ${expert.id})">
                                <i class="fa fa-question-circle"></i> 疑问
                            </button>
                            <button class="sidebar-annotation-btn" onclick="addComment(${annotationId}, ${expert.id})">
                                <i class="fa fa-comment"></i> 评论
                            </button>
                        </div>
                    </div>
                `;
            });
            
            // 填充侧边栏内容
            content.innerHTML = `
                <div class="sidebar-annotations">
                    ${annotationsHtml}
                </div>
            `;
            
            // 显示侧边栏
            sidebar.classList.remove('collapsed');
        }
    </script>
</body>
</html> 
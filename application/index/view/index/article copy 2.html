<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>《般若波罗蜜多心经》全文 - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
            color: #2c1810;
            margin: 0;
            padding: 0;
        }
        
        /* 内容容器 */
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 文章头部 */
        .article-header {
            background: white;
            border-radius: 12px;
            padding: 40px 30px;
            margin: 25px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .article-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c1810;
            margin-bottom: 20px;
            line-height: 1.3;
            text-align: center;
        }
        
        .article-meta {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: #8b4513;
            gap: 20px;
        }
        
        .expert-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid #e9ecef;
            object-fit: cover;
        }
        
        .expert-info {
            text-align: center;
        }
        
        .expert-name {
            color: #8b4513;
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .expert-title {
            color: #a67b5b;
            font-size: 0.9rem;
        }
        
        .article-date {
            color: #8b4513;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c1810;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #8b4513;
            padding-bottom: 10px;
        }
        
        /* 主要内容区域 */
        .main-content-area {
            display: flex;
            gap: 20px;
            margin: 30px 0;
        }
        

        
        /* 左侧文章内容区域 */
        .article-content-area {
            flex: 1;
            min-width: 0;
        }
        
        /* 文章内容 */
        .article-content {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            line-height: 1.8;
            font-size: 1.1rem;
            color: #2c1810;
        }
        
        /* 右侧专家批注侧边栏 */
        .annotation-sidebar {
            width: 350px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }
        
        .annotation-sidebar.collapsed {
            width: 50px;
        }
        
        .annotation-sidebar-header {
            background: linear-gradient(135deg, #8b4513, #a67b5b);
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .annotation-sidebar-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .annotation-count {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }
        
        .annotation-sidebar-toggle {
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .annotation-sidebar-toggle:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .annotation-sidebar.collapsed .annotation-sidebar-header h3 {
            display: none;
        }
        
        .annotation-sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 600px;
        }
        
        .annotation-sidebar.collapsed .annotation-sidebar-content {
            display: none;
        }
        
        /* 批注占位符 */
        .annotation-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 40px 20px;
        }
        
        .annotation-placeholder i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #dee2e6;
        }
        
        .annotation-placeholder p {
            margin: 0;
            font-size: 0.9rem;
        }
        
        /* 侧边栏批注样式 */
        .sidebar-annotations {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .sidebar-annotation-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        

        
        .sidebar-annotation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .sidebar-annotation-author {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .sidebar-annotation-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #e9ecef;
        }
        
        .sidebar-annotation-author-name {
            font-weight: 600;
            color: #8b4513;
            font-size: 0.9rem;
        }
        
        .sidebar-annotation-author-title {
            color: #a67b5b;
            font-size: 0.8rem;
        }
        
        .sidebar-annotation-type {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
        }
        
        .sidebar-annotation-type.add {
            background: #d4edda;
            color: #155724;
        }
        
        .sidebar-annotation-type.modify {
            background: #fff3cd;
            color: #856404;
        }
        
        .sidebar-annotation-type.delete {
            background: #f8d7da;
            color: #721c24;
        }
        
        .sidebar-annotation-content {
            color: #2c1810;
            line-height: 1.5;
            font-size: 0.9rem;
            margin-bottom: 12px;
        }
        
        .sidebar-annotation-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .sidebar-annotation-btn {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        
        .sidebar-annotation-btn:hover {
            background: #e9ecef;
        }
        
        .article-content h2 {
            color: #8b4513;
            font-weight: 600;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .article-content h3 {
            color: #a67b5b;
            font-weight: 600;
            margin-top: 25px;
            margin-bottom: 12px;
            font-size: 1.3rem;
        }
        
        .article-content p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        /* 专家批注 */
        .annotation-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        /* 批注组样式 */
        .annotation-group {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .annotation-group-header {
            background: linear-gradient(135deg, #8b4513, #a67b5b);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .annotation-group-title {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .annotation-group-count {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .annotation-item {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .annotation-item:last-child {
            border-bottom: none;
        }
        

        
        .annotation-item:hover {
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.1);
        }
        
        .annotation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .annotation-author {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .annotation-author img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #e9ecef;
        }
        
        .annotation-author-info {
            display: flex;
            flex-direction: column;
        }
        
        .annotation-author-name {
            font-weight: 600;
            color: #8b4513;
            font-size: 1rem;
        }
        
        .annotation-author-title {
            color: #a67b5b;
            font-size: 0.8rem;
        }
        
        .annotation-type {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .annotation-type.add {
            background: #d4edda;
            color: #155724;
        }
        
        .annotation-type.modify {
            background: #fff3cd;
            color: #856404;
        }
        
        .annotation-type.delete {
            background: #f8d7da;
            color: #721c24;
        }
        
        .annotation-content {
            color: #2c1810;
            line-height: 1.6;
            margin-bottom: 12px;
            font-size: 1rem;
        }
        
        .annotation-position {
            color: #8b4513;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        /* 批注投票区域 */
        .annotation-vote {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .annotation-vote-title {
            font-size: 0.9rem;
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .annotation-vote-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .annotation-vote-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }
        
        .annotation-vote-btn.support {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        
        .annotation-vote-btn.support:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
        
        .annotation-vote-btn.question {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }
        
        .annotation-vote-btn.question:hover {
            background: linear-gradient(135deg, #e0a800, #e8590c);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
        }
        
        .annotation-vote-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .annotation-vote-stats {
            color: #8b4513;
            font-size: 0.8rem;
        }
        
        /* 投票进度条 */
        .vote-progress {
            margin-top: 12px;
        }
        
        .vote-progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .vote-progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .vote-progress-fill.support {
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        
        .vote-progress-fill.question {
            background: linear-gradient(90deg, #ffc107, #fd7e14);
        }
        
        .vote-progress-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #8b4513;
        }
        
        .vote-progress-label {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .vote-progress-label.support {
            color: #28a745;
        }
        
        .vote-progress-label.question {
            color: #ffc107;
        }
        
        /* 批注评论区 */
        .annotation-comments {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .annotation-comment-form {
            margin-bottom: 15px;
        }
        
        .annotation-comment-textarea {
            border-radius: 6px;
            border: 2px solid #e9ecef;
            padding: 10px;
            resize: vertical;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            width: 100%;
            min-height: 60px;
        }
        
        .annotation-comment-textarea:focus {
            border-color: #8b4513;
            box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25);
            outline: none;
        }
        
        .annotation-comment-submit {
            background: linear-gradient(135deg, #8b4513, #a67b5b);
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-weight: 600;
            color: white;
            box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
            transition: all 0.3s ease;
            font-size: 0.9rem;
            margin-top: 8px;
        }
        
        .annotation-comment-submit:hover {
            background: linear-gradient(135deg, #a67b5b, #cd853f);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }
        
        .annotation-comment-item {
            border-bottom: 1px solid #e9ecef;
            padding: 12px 0;
        }
        
        .annotation-comment-item:last-child {
            border-bottom: none;
        }
        
        .annotation-comment-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 8px;
        }
        
        .annotation-comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #e9ecef;
            object-fit: cover;
        }
        
        .annotation-comment-author {
            font-weight: 600;
            color: #8b4513;
            font-size: 0.9rem;
        }
        
        .annotation-comment-date {
            color: #a67b5b;
            font-size: 0.8rem;
        }
        
        .annotation-comment-content {
            color: #2c1810;
            line-height: 1.5;
            font-size: 0.9rem;
        }
        
        /* 批注类型图例 */
        .annotation-legend {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .legend-items {
            display: flex;
            gap: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .legend-marker {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .legend-marker.add {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }
        
        .legend-marker.modify {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }
        
        .legend-marker.delete {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }
        
        .legend-text {
            font-size: 0.9rem;
            color: #2c1810;
            font-weight: 500;
        }
        
        /* 二维码区域 */
        .qr-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            background: #f8f9fa;
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #8b4513;
            transition: all 0.3s ease;
        }
        
        .qr-code:hover {
            border-color: #8b4513;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.1);
        }
        
        .qr-code i {
            font-size: 80px;
            margin-bottom: 10px;
            color: #a67b5b;
        }
        
        .qr-text {
            font-size: 0.9rem;
            color: #8b4513;
        }
        
        .qr-tip {
            color: #a67b5b;
            font-size: 0.9rem;
            margin-top: 15px;
        }
        

        
        /* 智能批注系统 */
        .smart-annotation-mode {
            position: relative;
        }
        

        
        /* 新的批注标签样式 */
        annotations {
            padding: 2px 4px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            display: inline;
            background: rgba(139, 69, 19, 0.1);
            border: 1px solid rgba(139, 69, 19, 0.2);
        }
        

        

        
        /* 专家批注工具栏 */
        .expert-toolbar {
            position: fixed;
            background: white;
            border-radius: 4px;
            padding: 2px 4px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
            border: 1px solid #e9ecef;
            z-index: 1000;
            display: none;
            flex-direction: row;
            align-items: center;
            gap: 3px;
            opacity: 0;
            visibility: hidden;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .expert-toolbar.show {
            display: flex !important;
            flex-direction: row !important;
            align-items: center;
            gap: 3px;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .expert-toolbar-btn {
            padding: 2px;
            border: none;
            border-radius: 2px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            color: #6c757d;
            width: 28px;
            height: 28px;
            position: relative;
        }
        
        .expert-toolbar-btn:hover {
            background: #f8f9fa;
            color: #495057;
            transform: translateY(-1px);
        }
        
        .expert-toolbar-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        /* 工具提示样式 */
        .expert-toolbar-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #495057;
            color: white;
            padding: 3px 6px;
            border-radius: 2px;
            font-size: 0.85rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
            z-index: 1001;
        }
        
        .expert-toolbar-btn:hover::after {
            opacity: 1;
            visibility: visible;
        }
        
        .expert-toolbar-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .expert-toolbar-separator {
            width: 1px;
            height: 20px;
            background: #dee2e6;
            margin: 0 4px;
        }
        
        /* 专家身份标识 */
        .expert-badge {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 8px;
        }
        
        /* 登录提示 */
        .login-prompt {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: #1976d2;
            margin-bottom: 20px;
        }
        
        /* 批注系统测试区域 */
        .annotation-test-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .annotation-test-header {
            text-align: center;
            margin-bottom: 25px;
        }
        
        .annotation-test-header h3 {
            color: #8b4513;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .annotation-test-header p {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .annotation-test-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .annotation-test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #8b4513, #a67b5b);
            color: white;
            box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
        }
        
        .annotation-test-btn:hover {
            background: linear-gradient(135deg, #a67b5b, #cd853f);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }
        
        .annotation-test-btn.clear {
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
        }
        
        .annotation-test-btn.clear:hover {
            background: linear-gradient(135deg, #c82333, #bd2130);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-container {
                padding: 0 15px;
            }
            
            .article-header {
                padding: 30px 20px;
                margin: 20px 0;
            }
            
            .article-title {
                font-size: 2rem;
            }
            
            .article-meta {
                flex-direction: column;
                gap: 15px;
            }
            
            .vote-buttons {
                flex-direction: column;
                gap: 15px;
            }
            
            .vote-btn {
                width: 100%;
                justify-content: center;
            }
            
            .article-content {
                padding: 30px 20px;
            }
            
            .annotation-section,
            .qr-section,
            .comment-section {
                padding: 25px 20px;
            }
            
            .annotation-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .annotation-vote-buttons {
                flex-direction: column;
                gap: 8px;
            }
            
            .annotation-vote-btn {
                width: 100%;
                justify-content: center;
            }
            
            .vote-progress-labels {
                flex-direction: column;
                gap: 4px;
            }
            
            .expert-editor-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .expert-editor-toggle {
                width: 100%;
                justify-content: center;
            }
            
            .expert-form-actions {
                flex-direction: column;
            }
            
            .expert-form-submit,
            .expert-form-cancel {
                width: 100%;
                justify-content: center;
            }
            
            .expert-toolbar {
                position: fixed !important;
                top: 10px !important;
                right: 10px;
                left: 10px;
                width: auto;
                flex-direction: row !important;
                flex-wrap: wrap;
                gap: 4px;
                transform: none !important;
            }
            
            .expert-toolbar-btn {
                padding: 2px;
                font-size: 1rem;
                width: 26px;
                height: 26px;
            }
            

            
            .legend-items {
                flex-direction: column;
                gap: 15px;
            }
            
            .legend-item {
                width: 100%;
                justify-content: center;
            }
            
            .annotation-popup {
                position: fixed;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 350px;
            }
            
            .qr-code {
                width: 160px;
                height: 160px;
            }
            
            .qr-code i {
                font-size: 60px;
            }
        }
        
        @media (max-width: 480px) {
            .article-header {
                padding: 25px 15px;
            }
            
            .article-title {
                font-size: 1.6rem;
            }
            
            .expert-avatar {
                width: 50px;
                height: 50px;
            }
            
            .article-content {
                padding: 25px 15px;
                font-size: 1rem;
            }
            
            .annotation-section,
            .qr-section,
            .comment-section {
                padding: 20px 15px;
            }
            
            .qr-code {
                width: 140px;
                height: 140px;
            }
            
            .qr-code i {
                font-size: 50px;
            }
            
            /* 移动端批注对话框 */
            .annotation-type-dialog-content,
            .annotation-content-dialog-content {
                width: 95%;
                max-width: none;
                margin: 10px;
            }
            
            /* 移动端测试区域 */
            .annotation-test-buttons {
                flex-direction: column;
                gap: 10px;
            }
            
            .annotation-test-btn {
                width: 100%;
                justify-content: center;
            }
            
            .annotation-type-options {
                flex-direction: column;
            }
            
            .annotation-content-actions {
                flex-direction: column;
            }
            
            .annotation-content-btn {
                width: 100%;
            }
            

            
            /* 移动端侧边栏 */
            .main-content-area {
                flex-direction: column;
            }
            
            .annotation-sidebar {
                width: 100%;
                margin-top: 20px;
            }
            
            .annotation-sidebar.collapsed {
                width: 100%;
                height: 50px;
            }
            
            .annotation-sidebar.collapsed .annotation-sidebar-content {
                display: none;
            }
        }
    </style>
</head>

<body>
    <!-- 引入公共头部导航 -->
    {include file="common/header" /}

    <!-- 内容区域 -->
    <div class="content-container">
        <!-- 文章头部 -->
        <section class="article-header">
            <h1 class="article-title">《般若波罗蜜多心经》全文</h1>
            
            <div class="article-meta">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=120&fit=crop&crop=face" class="expert-avatar" alt="专家头像">
                <div class="expert-info">
                    <div class="expert-name">释慧明法师</div>
                    <div class="expert-title">佛学专家 | 禅宗大师</div>
                </div>
                <div class="article-date">
                    <i class="fa fa-calendar"></i> 2024-01-15
                </div>
            </div>
        </section>

        <!-- 批注类型图例 -->
        <section class="annotation-legend">
            <h3 class="section-title">批注类型说明</h3>
                            <div class="legend-items">
                    <div class="legend-item">
                        <annotations1 data-type="add" style="pointer-events: none; padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(40, 167, 69, 0.3); border: 1px solid rgba(40, 167, 69, 0.6); color: #155724;">示例文本</annotations1>
                        <span class="legend-text">新增内容：专家建议添加的内容</span>
                    </div>
                    <div class="legend-item">
                        <annotations2 data-type="modify" style="pointer-events: none; padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.6); color: #856404;">示例文本</annotations2>
                        <span class="legend-text">修改内容：专家建议修改的内容</span>
                    </div>
                    <div class="legend-item">
                        <annotations3 data-type="delete" style="pointer-events: none; padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(220, 53, 69, 0.3); border: 1px solid rgba(220, 53, 69, 0.6); color: #721c24;">示例文本</annotations3>
                        <span class="legend-text">删除内容：专家建议删除的内容</span>
                    </div>
                </div>
        </section>

        <!-- 主要内容区域 -->
        <div class="main-content-area">
            <!-- 左侧文章内容 -->
            <div class="article-content-area">
                <!-- 专家批注工具栏 -->
                <div class="expert-toolbar" id="expertToolbar">
                    <button class="expert-toolbar-btn add" onclick="createAnnotation('add')" id="addBtn" data-tooltip="新增">
                        +
                    </button>
                    <button class="expert-toolbar-btn modify disabled" onclick="createAnnotation('modify')" id="modifyBtn" data-tooltip="修改">
                        ✏
                    </button>
                    <button class="expert-toolbar-btn delete disabled" onclick="createAnnotation('delete')" id="deleteBtn" data-tooltip="删除">
                        ×
                    </button>
                </div>
                
                <section class="article-content smart-annotation-mode">
                    <h2>般若波罗蜜多心经</h2>
                    <p><annotations1 data-type="add" data-annotation="1" onclick="showSidebarAnnotation(this, 'add')" style="padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(40, 167, 69, 0.3); border: 1px solid rgba(40, 167, 69, 0.6); color: #155724;">观自在菩萨</annotations1>，行深般若波罗蜜多时，照见五蕴皆空，度一切苦厄。舍利子，色不异空，空不异色，色即是空，空即是色，受想行识，亦复如是。</p>
                    
                    <p>舍利子，是诸法空相，不生不灭，不垢不净，<annotations6 data-type="modify" data-annotation="6" onclick="showSidebarAnnotation(this, 'modify')" style="padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.6); color: #856404;">不减。是故空中无色</annotations6>，无受想行识，无眼耳鼻舌身意，无色声香味触法，无眼界，乃至无意识界，无无明，亦无无明尽，乃至无老死，亦无老死尽。无苦集灭道，无智亦无得。</p>
                    
                    <p>舍利子，是诸法空相，不生不灭，不垢不净，不增不减。是故<annotations8 data-type="add" data-annotation="8" onclick="showSidebarAnnotation(this, 'add')" style="padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(40, 167, 69, 0.3); border: 1px solid rgba(40, 167, 69, 0.6); color: #155724;">空中无色</annotations8>，无受想行识，无眼耳鼻舌身意，无色声香味触法，无眼界，乃至无意识界，无无明，亦无无明尽，乃至无老死，亦无老死尽。无苦集灭道，无智亦无得。</p>
                    
                    <p>以无所得故，菩提萨埵，依般若波罗蜜多故，心无挂碍。无挂碍故，无有恐怖，远离颠倒梦想，<annotations7 data-type="add" data-annotation="7" onclick="showSidebarAnnotation(this, 'add')" style="padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(40, 167, 69, 0.3); border: 1px solid rgba(40, 167, 69, 0.6); color: #155724;">究竟涅槃</annotations7>。三世诸佛，依般若波罗蜜多故，得阿耨多罗三藐三菩提。</p>
                    
                    <p>故知般若波罗蜜多，是大神咒，是大明咒，是无上咒，是<annotations3 data-type="add" data-annotation="3" onclick="showSidebarAnnotation(this, 'add')" style="padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(40, 167, 69, 0.3); border: 1px solid rgba(40, 167, 69, 0.6); color: #155724;">无等等咒</annotations3>，能除一切苦，真实不虚。故说般若波罗蜜多咒，即说咒曰：揭谛揭谛，波罗揭谛，波罗僧揭谛，菩提萨婆诃。</p>
                    
                    <p>故知般若波罗蜜多，是大神咒，是大明咒，是无上咒，是无<annotations4 data-type="modify" data-annotation="4" onclick="showSidebarAnnotation(this, 'modify')" style="padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.6); color: #856404;">等等</annotations4>咒，能除一切苦，真实不虚。故说般若波罗蜜多咒，即说咒曰：揭谛揭谛，波罗揭谛，波罗僧揭谛，菩提萨婆诃。</p>
                    
                    <h2>经文释义</h2>
                    <h3>一、观自在菩萨</h3>
                    <p>观自在菩萨即观世音菩萨，以慈悲心观照世间众生，寻声救苦，故名观世音。菩萨行深般若波罗蜜多时，即是以甚深智慧观照诸法实相。</p>
                    
                    <h3>二、五蕴皆空</h3>
                    <p>五蕴即色、受、想、行、识。色蕴指物质世界，受蕴指感受，想蕴指思维，行蕴指意志活动，识蕴指认识作用。<annotations2 data-type="modify" data-annotation="2" onclick="showSidebarAnnotation(this, 'modify')" style="padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; display: inline; background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.6); color: #856404;">五蕴皆空</annotations2>，即是说一切现象都是因缘和合而生，没有独立的自性。</p>
                    
                    <h3>三、诸法空相</h3>
                    <p>诸法空相，不生不灭，不垢不净，不增不减。这是说明诸法的真实相状，即空性。空性不是虚无，而是超越有无的相对概念。</p>
                    
                    <h2>修行指导</h2>
                    <h3>一、观照般若</h3>
                    <p>修行者应当以般若智慧观照一切法，了知诸法如幻如化，无有实性。通过观照，能够破除执着，获得解脱。</p>
                    
                    <h3>二、心无挂碍</h3>
                    <p>依般若波罗蜜多，心无挂碍。无挂碍故，无有恐怖，远离颠倒梦想，究竟涅槃。这是说明般若智慧的作用。</p>
                    
                    <h3>三、究竟涅槃</h3>
                    <p>通过般若智慧的观照，能够达到究竟涅槃的境界，即彻底解脱生死轮回，获得永恒的安乐。</p>
                </section>
            </div>

            <!-- 右侧专家批注区域 -->
            <div class="annotation-sidebar" id="annotationSidebar">
                <div class="annotation-sidebar-header">
                    <h3>专家批注 <span class="annotation-count">(<span id="totalAnnotations">8</span>)</span></h3>
                    <div class="annotation-sidebar-toggle" onclick="toggleAnnotationSidebar()">
                        <i class="fa fa-chevron-left"></i>
                    </div>
                </div>
                <div class="annotation-sidebar-content" id="annotationSidebarContent">
                    <div class="annotation-placeholder">
                        <i class="fa fa-comments"></i>
                        <p>选择文本查看专家批注</p>
                    </div>
                </div>
            </div>
        </div>


        
        <!-- 批注系统测试区域 -->
        <section class="annotation-test-section">
            <div class="annotation-test-header">
                <h3>批注系统测试</h3>
                <p>当前系统支持无限数量的批注，点击下方按钮可以快速添加测试批注</p>
            </div>
            <div class="annotation-test-buttons">
                <button class="annotation-test-btn" onclick="addTestAnnotations(10)">
                    <i class="fa fa-plus"></i> 添加10个测试批注
                </button>
                <button class="annotation-test-btn" onclick="addTestAnnotations(50)">
                    <i class="fa fa-plus"></i> 添加50个测试批注
                </button>
                <button class="annotation-test-btn" onclick="addTestAnnotations(100)">
                    <i class="fa fa-plus"></i> 添加100个测试批注
                </button>
                <button class="annotation-test-btn" onclick="removeDuplicateAnnotations()" style="background: linear-gradient(135deg, #17a2b8, #138496); box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);">
                    <i class="fa fa-filter"></i> 清理重复批注
                </button>
                <button class="annotation-test-btn clear" onclick="clearAllAnnotations()">
                    <i class="fa fa-trash"></i> 清空所有批注
                </button>
            </div>
        </section>





        <!-- 入群二维码 -->
        <section class="qr-section">
            <h3 class="section-title">加入讨论群</h3>
            <p class="qr-tip">扫描下方二维码，加入微信群，与专家和其他读者一起讨论本文内容</p>
            
            <div class="qr-code">
                <i class="fa fa-qrcode"></i>
                <div class="qr-text">微信群二维码</div>
            </div>
            
            <p class="qr-tip">
                <i class="fa fa-info-circle"></i> 
                扫码加入佛经学习交流群
            </p>
        </section>


    </div>

    <!-- 引入公共页脚 -->
    {include file="common/footer" /}

    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
    <script>
        // 移除自定义元素注册，因为annotations标签不需要注册为自定义元素
        // 我们直接使用原生HTML标签即可
        // 批注投票功能
        function voteAnnotation(annotationId, type, expertId) {
            // 这里可以添加批注投票逻辑
            if (typeof layer !== 'undefined') {
                layer.msg('投票成功！感谢您的参与。', {icon: 1});
            } else {
                alert('投票成功！感谢您的参与。');
            }
            
            // 更新按钮状态
            const voteButtons = $(`.annotation-item:nth-child(${annotationId}) .annotation-vote-btn`);
            if (type === 'support') {
                voteButtons.filter('.support').addClass('active').prop('disabled', true);
                voteButtons.filter('.question').prop('disabled', true);
            } else {
                voteButtons.filter('.question').addClass('active').prop('disabled', true);
                voteButtons.filter('.support').prop('disabled', true);
            }
            
            // 更新进度条
            updateVoteProgress(annotationId, type, expertId);
        }
        
        // 更新投票进度条
        function updateVoteProgress(annotationId, type, expertId) {
            const annotationItem = $(`.annotation-item:nth-child(${annotationId})`);
            const progressFill = annotationItem.find('.vote-progress-fill');
            const voteLabels = annotationItem.find('.vote-progress-labels .vote-progress-label');
            const voteStats = annotationItem.find('.annotation-vote-stats');
            
            // 获取当前投票数据
            let supportCount = 15;
            let questionCount = 5;
            
            // 根据投票类型更新数据
            if (type === 'support') {
                supportCount++;
            } else {
                questionCount++;
            }
            
            const totalCount = supportCount + questionCount;
            const supportPercent = Math.round((supportCount / totalCount) * 100);
            const questionPercent = 100 - supportPercent;
            
            // 更新进度条
            progressFill.css('width', supportPercent + '%');
            
            // 更新标签文本
            if (voteLabels.length >= 2) {
                voteLabels.eq(0).find('.vote-count').text(`${supportCount}票 (${supportPercent}%)`);
                voteLabels.eq(1).find('.vote-count').text(`${questionCount}票 (${questionPercent}%)`);
                voteStats.find('.vote-total').text(`总计：${totalCount}票`);
            }
        }
        
        // 批注评论提交功能
        function submitAnnotationComment(event, annotationId, expertId) {
            event.preventDefault();
            const button = event.target;
            const commentForm = button.closest('.annotation-comments');
            const textarea = commentForm.querySelector('.annotation-comment-textarea');
            const content = textarea.value.trim();
            
            if (!content) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请输入评论内容', {icon: 2});
                } else {
                    alert('请输入评论内容');
                }
                return;
            }
            
            // 这里可以添加提交批注评论的逻辑
            if (typeof layer !== 'undefined') {
                layer.msg('评论发表成功！', {icon: 1});
            } else {
                alert('评论发表成功！');
            }
            
            // 清空文本框
            textarea.value = '';
        }
        
        // 智能批注系统
        let currentAnnotationType = null;
        let isAnnotationMode = false;
        let isExpertUser = true; // 模拟专家用户状态
        let currentSelectionRange = null; // 保存当前选中的文本范围
        let currentSelectedText = null; // 保存当前选中的文本内容
        
        // 动态批注管理器 - 支持无限数量的批注
        class AnnotationManager {
            constructor() {
                this.annotations = new Map(); // 使用Map存储所有批注数据
                this.nextAnnotationId = 1; // 下一个批注ID
                this.expertColors = ['expert-1', 'expert-2', 'expert-3', 'expert-4', 'expert-5'];
                this.expertAvatars = [
                    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face',
                    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face',
                    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=80&h=80&fit=crop&crop=face',
                    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face',
                    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=80&h=80&fit=crop&crop=face'
                ];
                this.expertNames = ['张教授', '李博士', '王教授', '陈博士', '刘教授', '孙教授', '赵教授', '钱博士', '周教授', '吴博士'];
                this.expertTitles = [
                    '佛学专家 | 北京大学',
                    '哲学专家 | 清华大学', 
                    '佛学专家 | 复旦大学',
                    '佛学专家 | 南京大学',
                    '佛学专家 | 中国社会科学院',
                    '佛学专家 | 浙江大学',
                    '佛学专家 | 武汉大学',
                    '哲学专家 | 中山大学',
                    '佛学专家 | 四川大学',
                    '哲学专家 | 南开大学'
                ];
                
                // 初始化一些示例批注数据
                this.initializeSampleAnnotations();
            }
            
            // 初始化示例批注数据
            initializeSampleAnnotations() {
                const sampleData = {
                    '1': {
                        experts: [
                            {
                                id: 1,
                                name: '张教授',
                                title: '佛学专家 | 北京大学',
                                avatar: this.expertAvatars[0],
                                color: this.expertColors[0],
                                type: 'add',
                                content: '观自在菩萨是观世音菩萨的别称，以慈悲心观照世间众生，寻声救苦。'
                            }
                        ]
                    },
                    '2': {
                        experts: [
                            {
                                id: 2,
                                name: '李博士',
                                title: '哲学专家 | 清华大学',
                                avatar: this.expertAvatars[1],
                                color: this.expertColors[1],
                                type: 'modify',
                                content: '五蕴皆空是佛教核心教义，指色、受、想、行、识五蕴都没有独立的自性。'
                            }
                        ]
                    },
                    '3': {
                        experts: [
                            {
                                id: 3,
                                name: '王教授',
                                title: '佛学专家 | 复旦大学',
                                avatar: this.expertAvatars[2],
                                color: this.expertColors[2],
                                type: 'add',
                                content: '无等等咒是般若波罗蜜多心经中的重要概念，表示无上、无等的咒语。'
                            }
                        ]
                    },
                    '4': {
                        experts: [
                            {
                                id: 4,
                                name: '陈博士',
                                title: '佛学专家 | 南京大学',
                                avatar: this.expertAvatars[3],
                                color: this.expertColors[3],
                                type: 'modify',
                                content: '等等表示平等、无差别，强调般若智慧的普遍性和平等性。'
                            }
                        ]
                    },
                    '5': {
                        experts: [
                            {
                                id: 5,
                                name: '刘教授',
                                title: '佛学专家 | 中国社会科学院',
                                avatar: this.expertAvatars[4],
                                color: this.expertColors[4],
                                type: 'add',
                                content: '空中无色是般若心经的核心教义，指在空性中没有任何固定的形相，一切法都是因缘和合而生，没有独立的自性。'
                            }
                        ]
                    },
                    '6': {
                        experts: [
                            {
                                id: 6,
                                name: '张教授',
                                title: '佛学专家 | 北京大学',
                                avatar: this.expertAvatars[0],
                                color: this.expertColors[0],
                                type: 'modify',
                                content: '不减。是故空中无色 - 这是般若心经的核心教义，指在空性中没有任何固定的形相，一切法都是因缘和合而生，没有独立的自性。'
                            }
                        ]
                    },
                    '7': {
                        experts: [
                            {
                                id: 7,
                                name: '孙教授',
                                title: '佛学专家 | 浙江大学',
                                avatar: this.expertAvatars[5],
                                color: this.expertColors[1],
                                type: 'add',
                                content: '究竟涅槃是佛教修行的最终目标，指彻底解脱生死轮回，获得永恒的安乐和智慧。'
                            }
                        ]
                    },
                    '8': {
                        experts: [
                            {
                                id: 8,
                                name: '李博士',
                                title: '佛学专家 | 清华大学',
                                avatar: this.expertAvatars[1],
                                color: this.expertColors[1],
                                type: 'add',
                                content: '是故空 - 这是般若心经中的重要转折，指因此缘故，一切法都是空性，没有固定的自性。'
                            }
                        ]
                    }
                };
                
                // 将示例数据添加到Map中
                for (let [key, value] of Object.entries(sampleData)) {
                    this.annotations.set(key, value);
                }
            }
            
            // 获取批注数据
            getAnnotation(annotationId) {
                return this.annotations.get(annotationId.toString()) || { experts: [] };
            }
            
            // 添加新批注
            addAnnotation(annotationId, expertData) {
                const existing = this.getAnnotation(annotationId);
                existing.experts.push(expertData);
                this.annotations.set(annotationId.toString(), existing);
            }
            
            // 生成新的批注ID
            generateAnnotationId() {
                return this.nextAnnotationId++;
            }
            
            // 获取随机专家信息
            getRandomExpert() {
                const randomIndex = Math.floor(Math.random() * this.expertNames.length);
                const colorIndex = randomIndex % this.expertColors.length;
                const avatarIndex = randomIndex % this.expertAvatars.length;
                
                return {
                    id: Date.now() + randomIndex,
                    name: this.expertNames[randomIndex],
                    title: this.expertTitles[randomIndex],
                    avatar: this.expertAvatars[avatarIndex],
                    color: this.expertColors[colorIndex]
                };
            }
            
            // 获取所有批注数量
            getTotalAnnotations() {
                return this.annotations.size;
            }
            
            // 获取所有批注数据
            getAllAnnotations() {
                return this.annotations;
            }
        }
        
        // 创建全局批注管理器实例
        const annotationManager = new AnnotationManager();
        

        
        // 更新工具栏位置的函数
        function updateToolbarPosition() {
            const toolbar = document.getElementById('expertToolbar');
            if (toolbar && toolbar.classList.contains('show')) {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const rect = range.getBoundingClientRect();
                    
                    console.log('更新工具栏位置，选中范围:', rect);
                    
                    // 更新工具栏位置
                    toolbar.style.position = 'fixed';
                    toolbar.style.top = (rect.top - 45) + 'px';
                    toolbar.style.left = (rect.left + rect.width / 2) + 'px';
                    toolbar.style.transform = 'translateX(-50%)';
                }
            }
        }
        
        // 显示专家工具栏
        function showExpertToolbar() {
            const toolbar = document.getElementById('expertToolbar');
            console.log('showExpertToolbar 被调用');
            console.log('toolbar 元素:', toolbar);
            if (toolbar) {
                toolbar.classList.add('show');
                console.log('已添加 show 类');
                updateToolbarPosition();
            } else {
                console.error('找不到工具栏元素');
            }
        }
        
        // 隐藏专家工具栏
        function hideExpertToolbar() {
            const toolbar = document.getElementById('expertToolbar');
            toolbar.classList.remove('show');
        }
        
        // 启用所有按钮
        function enableAllButtons() {
            const addBtn = document.getElementById('addBtn');
            const modifyBtn = document.getElementById('modifyBtn');
            const deleteBtn = document.getElementById('deleteBtn');
            
            addBtn.classList.remove('disabled');
            modifyBtn.classList.remove('disabled');
            deleteBtn.classList.remove('disabled');
        }
        

        
        // 隐藏专家工具栏
        function hideExpertToolbar() {
            const toolbar = document.getElementById('expertToolbar');
            toolbar.classList.remove('show');
        }
        
        // 智能文本选择处理
        function handleSmartTextSelection() {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            
            if (isExpertUser) {
                // 检查选中的文本是否在 article-content-area 内
                const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
                const articleContentArea = document.querySelector('.article-content-area');
                
                if (range && articleContentArea && articleContentArea.contains(range.commonAncestorContainer)) {
                    if (selectedText.length > 0) {
                        // 有选中文本，启用所有按钮（允许重叠批注）
                        enableAllButtons();
                        // 保存当前选中的范围
                        currentSelectionRange = range.cloneRange();
                        // 保存选中的文本内容
                        currentSelectedText = selectedText;
                        // 显示工具栏
                        showExpertToolbar();
                    } else {
                        // 没有选中文本，隐藏工具栏
                        hideExpertToolbar();
                    }
                } else {
                    // 不在指定区域内，隐藏工具栏
                    hideExpertToolbar();
                }
            }
        }
        
        // 显示批注类型选择对话框
        function showAnnotationTypeDialog(selectedText) {
            const dialog = document.createElement('div');
            dialog.className = 'annotation-type-dialog';
            dialog.innerHTML = `
                <div class="annotation-type-dialog-content">
                    <div class="annotation-type-dialog-header">
                        <h3>选择批注类型</h3>
                        <button onclick="closeAnnotationTypeDialog()">×</button>
                    </div>
                    <div class="annotation-type-dialog-body">
                        <p>您选择了："${selectedText}"</p>
                        <div class="annotation-type-options">
                            <button class="annotation-type-btn add" onclick="createAnnotation('add', '${selectedText.replace(/'/g, "\\'")}')">
                                <i class="fa fa-plus"></i> 新增内容
                            </button>
                            <button class="annotation-type-btn modify" onclick="createAnnotation('modify', '${selectedText.replace(/'/g, "\\'")}')">
                                <i class="fa fa-edit"></i> 修改内容
                            </button>
                            <button class="annotation-type-btn delete" onclick="createAnnotation('delete', '${selectedText.replace(/'/g, "\\'")}')">
                                <i class="fa fa-trash"></i> 删除内容
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // 添加样式
            if (!document.getElementById('annotation-type-dialog-styles')) {
                const style = document.createElement('style');
                style.id = 'annotation-type-dialog-styles';
                style.textContent = `
                    .annotation-type-dialog {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                    }
                    
                    .annotation-type-dialog-content {
                        background: white;
                        border-radius: 12px;
                        padding: 20px;
                        max-width: 400px;
                        width: 90%;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    }
                    
                    .annotation-type-dialog-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 15px;
                        padding-bottom: 10px;
                        border-bottom: 1px solid #e9ecef;
                    }
                    
                    .annotation-type-dialog-header h3 {
                        margin: 0;
                        color: #8b4513;
                        font-size: 1.1rem;
                    }
                    
                    .annotation-type-dialog-header button {
                        background: none;
                        border: none;
                        font-size: 1.5rem;
                        color: #999;
                        cursor: pointer;
                    }
                    
                    .annotation-type-options {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                        margin-top: 15px;
                    }
                    
                    .annotation-type-btn {
                        padding: 12px 16px;
                        border: none;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                    
                    .annotation-type-btn.add {
                        background: #d4edda;
                        color: #155724;
                    }
                    
                    .annotation-type-btn.add:hover {
                        background: #c3e6cb;
                    }
                    
                    .annotation-type-btn.modify {
                        background: #fff3cd;
                        color: #856404;
                    }
                    
                    .annotation-type-btn.modify:hover {
                        background: #ffeaa7;
                    }
                    
                    .annotation-type-btn.delete {
                        background: #f8d7da;
                        color: #721c24;
                    }
                    
                    .annotation-type-btn.delete:hover {
                        background: #f5c6cb;
                    }
                `;
                document.head.appendChild(style);
            }
        }
        
        // 关闭批注类型对话框
        function closeAnnotationTypeDialog() {
            const dialog = document.querySelector('.annotation-type-dialog');
            if (dialog) {
                dialog.remove();
            }
        }
        
        // 创建批注
        function createAnnotation(type) {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            
            if (selectedText.length > 0) {
                showAnnotationContentDialog(type, selectedText);
            } else {
                if (typeof layer !== 'undefined') {
                    layer.msg('请先选择要批注的文本', {icon: 2});
                } else {
                    alert('请先选择要批注的文本');
                }
            }
        }
        
        // 显示批注内容输入对话框
        function showAnnotationContentDialog(type, text) {
            const dialog = document.createElement('div');
            dialog.className = 'annotation-content-dialog';
            dialog.innerHTML = `
                <div class="annotation-content-dialog-content">
                    <div class="annotation-content-dialog-header">
                        <h3>${getTypeText(type)}批注</h3>
                        <button onclick="closeAnnotationContentDialog()">×</button>
                    </div>
                    <div class="annotation-content-dialog-body">
                        <div class="annotation-content-info">
                            <p><strong>选中的文本：</strong>"${text}"</p>
                            <p><strong>批注类型：</strong><span class="annotation-type-badge ${type}">${getTypeText(type)}</span></p>
                        </div>
                        <div class="annotation-content-form">
                            <label for="annotation-content">请输入您的批注内容：</label>
                            <textarea id="annotation-content" placeholder="请详细说明您的批注内容..." rows="6"></textarea>
                            <div class="annotation-content-actions">
                                <button class="annotation-content-btn primary" onclick="submitAnnotationContent('${type}', '${text.replace(/'/g, "\\'")}')">
                                    <i class="fa fa-check"></i> 提交批注
                                </button>
                                <button class="annotation-content-btn secondary" onclick="closeAnnotationContentDialog()">
                                    <i class="fa fa-times"></i> 取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // 添加样式
            if (!document.getElementById('annotation-content-dialog-styles')) {
                const style = document.createElement('style');
                style.id = 'annotation-content-dialog-styles';
                style.textContent = `
                    .annotation-content-dialog {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                    }
                    
                    .annotation-content-dialog-content {
                        background: white;
                        border-radius: 12px;
                        padding: 0;
                        max-width: 600px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    }
                    
                    .annotation-content-dialog-header {
                        background: linear-gradient(135deg, #8b4513, #a67b5b);
                        color: white;
                        padding: 20px;
                        border-radius: 12px 12px 0 0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .annotation-content-dialog-header h3 {
                        margin: 0;
                        font-size: 1.2rem;
                        font-weight: 600;
                    }
                    
                    .annotation-content-dialog-header button {
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.5rem;
                        cursor: pointer;
                        padding: 0;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .annotation-content-dialog-body {
                        padding: 20px;
                    }
                    
                    .annotation-content-info {
                        background: #f8f9fa;
                        border-radius: 8px;
                        padding: 15px;
                        margin-bottom: 20px;
                        border-left: 4px solid #8b4513;
                    }
                    
                    .annotation-content-info p {
                        margin: 0 0 8px 0;
                        color: #2c1810;
                    }
                    
                    .annotation-content-info p:last-child {
                        margin-bottom: 0;
                    }
                    
                    .annotation-type-badge {
                        display: inline-block;
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-size: 0.8rem;
                        font-weight: 600;
                    }
                    
                    .annotation-type-badge.add {
                        background: #d4edda;
                        color: #155724;
                    }
                    
                    .annotation-type-badge.modify {
                        background: #fff3cd;
                        color: #856404;
                    }
                    
                    .annotation-type-badge.delete {
                        background: #f8d7da;
                        color: #721c24;
                    }
                    
                    .annotation-content-form label {
                        display: block;
                        margin-bottom: 8px;
                        font-weight: 600;
                        color: #2c1810;
                    }
                    
                    .annotation-content-form textarea {
                        width: 100%;
                        border: 2px solid #e9ecef;
                        border-radius: 8px;
                        padding: 12px;
                        font-size: 0.9rem;
                        line-height: 1.5;
                        resize: vertical;
                        font-family: inherit;
                    }
                    
                    .annotation-content-form textarea:focus {
                        outline: none;
                        border-color: #8b4513;
                        box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
                    }
                    
                    .annotation-content-actions {
                        display: flex;
                        gap: 10px;
                        margin-top: 15px;
                        justify-content: flex-end;
                    }
                    
                    .annotation-content-btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        font-size: 0.9rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                    }
                    
                    .annotation-content-btn.primary {
                        background: #8b4513;
                        color: white;
                    }
                    
                    .annotation-content-btn.primary:hover {
                        background: #a67b5b;
                    }
                    
                    .annotation-content-btn.secondary {
                        background: #f8f9fa;
                        color: #6c757d;
                        border: 1px solid #dee2e6;
                    }
                    
                    .annotation-content-btn.secondary:hover {
                        background: #e9ecef;
                    }
                `;
                document.head.appendChild(style);
            }
        }
        
        // 关闭批注内容输入对话框
        function closeAnnotationContentDialog() {
            const dialog = document.querySelector('.annotation-content-dialog');
            if (dialog) {
                dialog.remove();
            }
            // 清除保存的范围
            currentSelectionRange = null;
        }
        
        // 提交批注内容
        function submitAnnotationContent(type, text) {
            const textarea = document.getElementById('annotation-content');
            const content = textarea.value.trim();
            // 使用保存的文本内容，如果没有则使用参数中的文本

            const selectedText = currentSelectedText || text;
            if (!content) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请输入批注内容', {icon: 2});
                } else {
                    alert('请输入批注内容');
                }
                return;
            }
            console.log('HTML内容:', content);
            console.log('文本内容:', selectedText);
            console.log('选中的HTML:', currentSelectionRange.cloneContents().outerHTML);// 获取选中内容的HTML
            console.log('选中的HTML:',  currentSelectionRange.toString());// 获取选中内容的HTML
            console.log('选中123121的HTML:',  text);// 获取选中内容的HTML
            console.log('选中的1213HTML:',  type);// 获取选中内容的HTML
            return
            
           
            
            // 检查是否已经存在相同文本的批注
            const existingAnnotations = document.querySelectorAll('annotations, [class*="annotations"]');
            let isDuplicate = false;
            let duplicateText = '';
            
            existingAnnotations.forEach(existingAnnotation => {
                const existingText = existingAnnotation.textContent.trim();
                const currentText = selectedText.trim();
                
                // 检查完全相同的文本
                if (existingText === currentText) {
                    isDuplicate = true;
                    duplicateText = existingText;
                }
                // 检查部分重叠的文本
                else if (existingText.includes(currentText) || currentText.includes(existingText)) {
                    isDuplicate = true;
                    duplicateText = existingText;
                }
            });
            
            if (isDuplicate) {
                if (typeof layer !== 'undefined') {
                    layer.msg(`该文本"${duplicateText}"已经被批注过了，请选择其他文本`, {icon: 2});
                } else {
                    alert(`该文本"${duplicateText}"已经被批注过了，请选择其他文本`);
                }
                
                // 清除保存的范围和文本
                currentSelectionRange = null;
                currentSelectedText = null;
                
                // 关闭对话框
                closeAnnotationContentDialog();
                
                // 隐藏工具栏
                hideExpertToolbar();
                return;
            }
            
            // 使用保存的文本范围
            if (currentSelectionRange) {
                // try {
                    // 生成新的批注ID
                    const annotationId = annotationManager.generateAnnotationId();
                    
                    // 创建新的annotations标签（带数字ID）
                    const annotationElement = document.createElement(`annotations${annotationId}`);
                    annotationElement.setAttribute('data-annotation', annotationId);
                    annotationElement.setAttribute('data-type', type);
                    annotationElement.setAttribute('data-annotation-content', content);
                    annotationElement.textContent = selectedText;
                    
                    // 根据类型设置样式
                    let style = 'padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: text; -webkit-user-select: text; -moz-user-select: text; -ms-user-select: text; display: inline;';
                    if (type === 'add') {
                        style += 'background: rgba(40, 167, 69, 0.3); border: 1px solid rgba(40, 167, 69, 0.6); color: #155724;';
                    } else if (type === 'modify') {
                        style += 'background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.6); color: #856404;';
                    } else if (type === 'delete') {
                        style += 'background: rgba(220, 53, 69, 0.3); border: 1px solid rgba(220, 53, 69, 0.6); color: #721c24;';
                    }
                    annotationElement.setAttribute('style', style);
                    
                    annotationElement.onclick = function() {
                        showSidebarAnnotation(this, type);
                    };
                    
                    // 添加hover效果
                    annotationElement.addEventListener('mouseenter', function() {
                        this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                        this.style.transform = 'translateY(-1px)';
                        this.style.background = type === 'add' ? 'rgba(40, 167, 69, 0.4)' : 
                                               type === 'modify' ? 'rgba(255, 193, 7, 0.4)' : 
                                               'rgba(220, 53, 69, 0.4)';
                    });
                    
                    annotationElement.addEventListener('mouseleave', function() {
                        this.style.boxShadow = '';
                        this.style.transform = '';
                        this.style.background = type === 'add' ? 'rgba(40, 167, 69, 0.3)' : 
                                               type === 'modify' ? 'rgba(255, 193, 7, 0.3)' : 
                                               'rgba(220, 53, 69, 0.3)';
                    });
                    
                    // 允许选择批注内的文本，但防止批注元素本身被选中
                    // annotationElement.addEventListener('mousedown', function(e) {
                    //     // 如果点击的是批注元素本身而不是其内容，则阻止选择
                    //     // if (e.target === this) {
                    //     //     e.preventDefault();
                    //     //     return false;
                    //     // }
                    // });
                    // 验证并重新获取当前选择范围
                    let rangeToUse = currentSelectionRange;
                    
                    // 检查保存的范围是否仍然有效
                    try {
                        // 尝试获取当前选择
                        const selection = window.getSelection();
                        if (selection.rangeCount > 0) {
                            const currentRange = selection.getRangeAt(0);
                            // 如果当前选择与保存的选择不同，使用当前选择
                            if (currentRange.toString() === text) {
                                rangeToUse = currentRange.cloneRange();
                            }
                        }
                    } catch (e) {
                        console.log('无法获取当前选择范围，使用保存的范围');
                    }
                    
                    // 使用重叠方式创建批注
                    if (rangeToUse && rangeToUse.toString().trim() === selectedText.trim()) {
                        console.log("带飞机哦",annotationElement)
                        console.log("选中的文本:", selectedText);
                        console.log("Range内容:", rangeToUse.toString());
                        console.log("Range起始位置:", rangeToUse.startOffset);
                        console.log("Range结束位置:", rangeToUse.endOffset);
                        
                                            // 获取Range的边界信息
                    const startContainer = rangeToUse.startContainer;
                    const endContainer = rangeToUse.endContainer;
                    const startOffset = rangeToUse.startOffset;
                    const endOffset = rangeToUse.endOffset;
                    
                    // 使用备用方法：删除内容并插入批注元素
                    rangeToUse.deleteContents();
                    // 在批注元素内添加文本节点
                    const annotationTextNode = document.createTextNode(selectedText);
                    annotationElement.appendChild(annotationTextNode);
                    rangeToUse.insertNode(annotationElement);
                    } else {
                        throw new Error('选择范围无效或已发生变化');
                    }
                    
                    // 将新批注添加到管理器（作为当前专家）
                    const currentExpertData = {
                        id: Date.now(),
                        name: '当前专家',
                        title: '专家用户',
                        avatar: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=80&h=80&fit=crop&crop=face',
                        color: 'expert-1',
                        type: type,
                        content: content
                    };
                    annotationManager.addAnnotation(annotationId, currentExpertData);
                    
                    // 更新批注计数
                    updateAnnotationCount();
                    
                    // 清除保存的范围和文本
                    currentSelectionRange = null;
                    currentSelectedText = null;
                    
                    // 关闭对话框
                    closeAnnotationContentDialog();
                    
                    // 隐藏工具栏
                    hideExpertToolbar();
                    
                    if (typeof layer !== 'undefined') {
                        layer.msg('批注已成功添加！', {icon: 1});
                    } else {
                        alert('批注已成功添加！');
                    }
                // } catch (error) {
                //     console.error('批注创建失败:', error);
                    
                //     // 清除保存的范围和文本
                //     currentSelectionRange = null;
                //     currentSelectedText = null;
                    
                //     // 关闭对话框
                //     closeAnnotationContentDialog();
                    
                //     // 隐藏工具栏
                //     hideExpertToolbar();
                    
                //     if (typeof layer !== 'undefined') {
                //         layer.msg('批注创建失败，请重新选择文本', {icon: 2});
                //     } else {
                //         alert('批注创建失败，请重新选择文本');
                //     }
                // }
            } else {
                if (typeof layer !== 'undefined') {
                    layer.msg('批注创建失败，请重新选择位置', {icon: 2});
                } else {
                    alert('批注创建失败，请重新选择位置');
                }
            }
        }
        
        // 显示侧边栏批注
        function showSidebarAnnotation(element, type) {
            const sidebar = document.getElementById('annotationSidebar');
            const content = document.getElementById('annotationSidebarContent');
            
            // 获取该位置的批注数据
            const annotationId = element.getAttribute('data-annotation') || Math.floor(Math.random() * 1000);
            const annotations = annotationManager.getAnnotation(annotationId);
            
            // 直接使用已有的专家批注，不再重复添加当前专家
            const allExperts = [...annotations.experts];
            
            // 生成批注HTML
            let annotationsHtml = '';
            allExperts.forEach((expert, index) => {
                annotationsHtml += `
                    <div class="sidebar-annotation-item ${expert.color}">
                        <div class="sidebar-annotation-header">
                            <div class="sidebar-annotation-author">
                                <img src="${expert.avatar}" class="sidebar-annotation-avatar" alt="专家头像">
                                <div class="sidebar-annotation-author-info">
                                    <div class="sidebar-annotation-author-name">${expert.name}</div>
                                    <div class="sidebar-annotation-author-title">${expert.title}</div>
                                </div>
                            </div>
                            <span class="sidebar-annotation-type ${expert.type}">${getTypeText(expert.type)}</span>
                        </div>
                        <div class="sidebar-annotation-content">
                            ${expert.content}
                        </div>
                        <div class="sidebar-annotation-actions">
                            <button class="sidebar-annotation-btn" onclick="voteAnnotation(${annotationId}, 'support', ${expert.id})">
                                <i class="fa fa-thumbs-up"></i> 支持
                            </button>
                            <button class="sidebar-annotation-btn" onclick="voteAnnotation(${annotationId}, 'question', ${expert.id})">
                                <i class="fa fa-question-circle"></i> 疑问
                            </button>
                            <button class="sidebar-annotation-btn" onclick="addComment(${annotationId}, ${expert.id})">
                                <i class="fa fa-comment"></i> 评论
                            </button>
                        </div>
                    </div>
                `;
            });
            
            // 填充侧边栏内容
            content.innerHTML = `
                <div class="sidebar-annotations">
                    ${annotationsHtml}
                </div>
            `;
            
            // 显示侧边栏
            sidebar.classList.remove('collapsed');
        }
        
        // 切换侧边栏显示/隐藏
        function toggleAnnotationSidebar() {
            const sidebar = document.getElementById('annotationSidebar');
            sidebar.classList.toggle('collapsed');
        }
        
        // 添加评论
        function addComment(annotationId, expertId) {
            const commentText = prompt('请输入您的评论：');
            if (commentText && commentText.trim()) {
                if (typeof layer !== 'undefined') {
                    layer.msg('评论已添加！', {icon: 1});
                } else {
                    alert('评论已添加！');
                }
            }
        }
        
        // 切换批注标签页
        function switchAnnotationTab(index) {
            const tabs = document.querySelectorAll('.annotation-popup-tab');
            const contents = document.querySelectorAll('.annotation-popup-tab-content');
            
            tabs.forEach((tab, i) => {
                tab.classList.toggle('active', i === index);
            });
            
            contents.forEach((content, i) => {
                content.classList.toggle('active', i === index);
            });
        }
        
        // 滚动到批注区域
        function scrollToAnnotationSection(annotationId) {
            const annotationSection = document.querySelector('.annotation-section');
            if (annotationSection) {
                annotationSection.scrollIntoView({ behavior: 'smooth' });
                closeAnnotationPopup();
                
                // 高亮对应的批注项
                const annotationItems = document.querySelectorAll('.annotation-item');
                annotationItems.forEach(item => {
                    item.style.border = 'none';
                });
                
                // 这里可以根据annotationId找到对应的批注项并高亮
                if (typeof layer !== 'undefined') {
                    layer.msg('已定位到批注区域', {icon: 1});
                }
            }
        }
        

        
        // 获取批注类型文本
        function getTypeText(type) {
            const typeMap = {
                'add': '新增内容',
                'modify': '修改内容',
                'delete': '删除内容'
            };
            return typeMap[type] || '未知类型';
        }
        
        // 添加测试批注
        function addTestAnnotations(count) {
            const testTexts = [
                '般若波罗蜜多',
                '观自在菩萨',
                '五蕴皆空',
                '诸法空相',
                '不生不灭',
                '不垢不净',
                '不增不减',
                '空中无色',
                '无受想行识',
                '无眼耳鼻舌身意',
                '无色声香味触法',
                '无眼界',
                '乃至无意识界',
                '无无明',
                '亦无无明尽',
                '乃至无老死',
                '亦无老死尽',
                '无苦集灭道',
                '无智亦无得',
                '菩提萨埵',
                '依般若波罗蜜多故',
                '心无挂碍',
                '无挂碍故',
                '无有恐怖',
                '远离颠倒梦想',
                '究竟涅槃',
                '三世诸佛',
                '得阿耨多罗三藐三菩提',
                '是大神咒',
                '是大明咒',
                '是无上咒',
                '是无等等咒',
                '能除一切苦',
                '真实不虚',
                '揭谛揭谛',
                '波罗揭谛',
                '波罗僧揭谛',
                '菩提萨婆诃'
            ];
            
            const types = ['add', 'modify', 'delete'];
            let addedCount = 0;
            let attempts = 0;
            const maxAttempts = count * 3; // 最多尝试3倍次数
            
            while (addedCount < count && attempts < maxAttempts) {
                attempts++;
                const annotationId = annotationManager.generateAnnotationId();
                const randomText = testTexts[Math.floor(Math.random() * testTexts.length)];
                const randomType = types[Math.floor(Math.random() * types.length)];
                const randomExpert = annotationManager.getRandomExpert();
                
                // 检查是否已经存在相同文本的批注
                const existingAnnotations = document.querySelectorAll('annotations, [class*="annotations"]');
                let isDuplicate = false;
                
                existingAnnotations.forEach(existingAnnotation => {
                    const existingText = existingAnnotation.textContent.trim();
                    const currentText = randomText.trim();
                    
                    // 检查完全相同的文本
                    if (existingText === currentText) {
                        isDuplicate = true;
                    }
                    // 检查部分重叠的文本
                    else if (existingText.includes(currentText) || currentText.includes(existingText)) {
                        isDuplicate = true;
                    }
                });
                
                if (isDuplicate) {
                    continue; // 跳过重复的文本
                }
                
                // 创建批注元素（带数字ID）
                const annotationElement = document.createElement(`annotations${annotationId}`);
                annotationElement.setAttribute('data-annotation', annotationId);
                annotationElement.setAttribute('data-type', randomType);
                annotationElement.setAttribute('data-annotation-content', `这是第${annotationId}个测试批注的内容，类型为${getTypeText(randomType)}。`);
                annotationElement.textContent = randomText;
                
                // 根据类型设置样式
                let style = 'padding: 2px 4px; border-radius: 3px; cursor: pointer; transition: all 0.3s ease; position: relative; user-select: text; -webkit-user-select: text; -moz-user-select: text; -ms-user-select: text; display: inline;';
                if (randomType === 'add') {
                    style += 'background: rgba(40, 167, 69, 0.3); border: 1px solid rgba(40, 167, 69, 0.6); color: #155724;';
                } else if (randomType === 'modify') {
                    style += 'background: rgba(255, 193, 7, 0.3); border: 1px solid rgba(255, 193, 7, 0.6); color: #856404;';
                } else if (randomType === 'delete') {
                    style += 'background: rgba(220, 53, 69, 0.3); border: 1px solid rgba(220, 53, 69, 0.6); color: #721c24;';
                }
                annotationElement.setAttribute('style', style);
                
                annotationElement.onclick = function() {
                    showSidebarAnnotation(this, randomType);
                };
                
                // 添加hover效果
                annotationElement.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                    this.style.transform = 'translateY(-1px)';
                    this.style.background = randomType === 'add' ? 'rgba(40, 167, 69, 0.4)' : 
                                           randomType === 'modify' ? 'rgba(255, 193, 7, 0.4)' : 
                                           'rgba(220, 53, 69, 0.4)';
                });
                
                annotationElement.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '';
                    this.style.transform = '';
                    this.style.background = randomType === 'add' ? 'rgba(40, 167, 69, 0.3)' : 
                                           randomType === 'modify' ? 'rgba(255, 193, 7, 0.3)' : 
                                           'rgba(220, 53, 69, 0.3)';
                });
                
                // 允许选择批注内的文本，但防止批注元素本身被选中
                annotationElement.addEventListener('mousedown', function(e) {
                    // 如果点击的是批注元素本身而不是其内容，则阻止选择
                    if (e.target === this) {
                        e.preventDefault();
                        return false;
                    }
                });
                
                // 添加到文章内容中（在第一个段落后插入）
                const articleContent = document.querySelector('.article-content');
                const firstParagraph = articleContent.querySelector('p');
                if (firstParagraph) {
                    firstParagraph.parentNode.insertBefore(annotationElement, firstParagraph.nextSibling);
                }
                
                // 将批注添加到管理器
                const expertData = {
                    ...randomExpert,
                    type: randomType,
                    content: `这是第${annotationId}个测试批注的内容，类型为${getTypeText(randomType)}。`
                };
                annotationManager.addAnnotation(annotationId, expertData);
                
                // 增加成功添加的计数
                addedCount++;
            }
            
            // 更新批注计数
            updateAnnotationCount();
            
            if (typeof layer !== 'undefined') {
                layer.msg(`已成功添加${addedCount}个测试批注！`, {icon: 1});
            } else {
                alert(`已成功添加${addedCount}个测试批注！`);
            }
        }
        
        // 清空所有批注
        function clearAllAnnotations() {
            // 移除所有批注元素（使用通配符选择器）
            const allElements = document.querySelectorAll('*');
            const annotationElements = Array.from(allElements).filter(element => {
                const tagName = element.tagName.toLowerCase();
                return tagName === 'annotations' || tagName.startsWith('annotations');
            });
            
            annotationElements.forEach(element => {
                element.remove();
            });
            
            // 清空批注管理器
            annotationManager.annotations.clear();
            annotationManager.nextAnnotationId = 1;
            
            // 重新初始化示例批注
            annotationManager.initializeSampleAnnotations();
            
            // 更新批注计数
            updateAnnotationCount();
            
            if (typeof layer !== 'undefined') {
                layer.msg('已清空所有批注！', {icon: 1});
            } else {
                alert('已清空所有批注！');
            }
        }
        
        // 清理重复批注
        function removeDuplicateAnnotations() {
            const allElements = document.querySelectorAll('*');
            const annotationElements = Array.from(allElements).filter(element => {
                const tagName = element.tagName.toLowerCase();
                return tagName === 'annotations' || tagName.startsWith('annotations');
            });
            
            const seenTexts = new Set();
            let removedCount = 0;
            
            annotationElements.forEach(element => {
                const text = element.textContent.trim();
                if (seenTexts.has(text)) {
                    // 这是重复的批注，移除它
                    element.remove();
                    removedCount++;
                } else {
                    seenTexts.add(text);
                }
            });
            
            // 更新批注计数
            updateAnnotationCount();
            
            if (typeof layer !== 'undefined') {
                layer.msg(`已清理${removedCount}个重复批注！`, {icon: 1});
            } else {
                alert(`已清理${removedCount}个重复批注！`);
            }
        }
        
        // 更新批注计数显示
        function updateAnnotationCount() {
            const countElement = document.getElementById('totalAnnotations');
            if (countElement) {
                countElement.textContent = annotationManager.getTotalAnnotations();
            }
        }
        
        // 为现有批注标签添加hover效果和允许文本选择
        function addHoverEffectsToExistingAnnotations() {
            const allElements = document.querySelectorAll('*');
            const annotationElements = Array.from(allElements).filter(element => {
                const tagName = element.tagName.toLowerCase();
                return tagName === 'annotations' || tagName.startsWith('annotations');
            });
            
            annotationElements.forEach(element => {
                const type = element.getAttribute('data-type');
                if (type) {
                    // 允许文本选择
                    element.style.userSelect = 'text';
                    element.style.webkitUserSelect = 'text';
                    element.style.mozUserSelect = 'text';
                    element.style.msUserSelect = 'text';
                    
                    element.addEventListener('mouseenter', function() {
                        this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                        this.style.transform = 'translateY(-1px)';
                        this.style.background = type === 'add' ? 'rgba(40, 167, 69, 0.4)' : 
                                               type === 'modify' ? 'rgba(255, 193, 7, 0.4)' : 
                                               'rgba(220, 53, 69, 0.4)';
                    });
                    
                    element.addEventListener('mouseleave', function() {
                        this.style.boxShadow = '';
                        this.style.transform = '';
                        this.style.background = type === 'add' ? 'rgba(40, 167, 69, 0.3)' : 
                                               type === 'modify' ? 'rgba(255, 193, 7, 0.3)' : 
                                               'rgba(220, 53, 69, 0.3)';
                    });
                }
            });
        }
        
        // 页面加载时设置文本选择事件
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            // 只在 article-content-area 内添加文本选择事件
            const articleContentArea = document.querySelector('.article-content-area');
            console.log('articleContentArea:', articleContentArea);
            if (articleContentArea) {
                articleContentArea.addEventListener('mouseup', handleSmartTextSelection);
                console.log('已添加mouseup事件监听器');
                

            } else {
                console.error('找不到 article-content-area 元素');
            }
            
            // 点击其他地方时隐藏工具栏
            document.addEventListener('click', function(event) {
                const toolbar = document.getElementById('expertToolbar');
                if (toolbar && !toolbar.contains(event.target) && !articleContentArea.contains(event.target)) {
                    hideExpertToolbar();
                }
            });
            
            // 初始化批注计数
            updateAnnotationCount();
            
            // 为现有批注标签添加hover效果
            addHoverEffectsToExistingAnnotations();
            
            // 滚动时更新工具栏位置
            document.addEventListener('scroll', function() {
                updateToolbarPosition();
            });
            
            // 窗口大小改变时更新工具栏位置
            window.addEventListener('resize', function() {
                updateToolbarPosition();
            });
            
            // 模拟专家用户登录状态
            if (isExpertUser) {
                // 不自动显示工具栏，只有在选中文本时才显示
                // setTimeout(() => {
                //     showExpertToolbar();
                // }, 1000);
            }
        });

        // 显示批注弹窗（已废弃，改为使用侧边栏）
        function showAnnotationPopup(element, annotationId) {
            // 直接调用侧边栏显示函数
            const type = element.getAttribute('data-type') || 'add';
            showSidebarAnnotation(element, type);
        }
    </script>
</body>
</html> 
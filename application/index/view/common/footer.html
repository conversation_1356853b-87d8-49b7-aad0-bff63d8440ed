<!-- 页脚 -->
<footer class="footer">
    <div class="container">
        <div class="footer-content">
            <div class="copyright">
                <i class="fa fa-copyright"></i> 
                <span>{:date('Y')} 佛经网. All rights reserved.</span>
            </div>
            <div class="footer-links">
                <a href="https://beian.miit.gov.cn" target="_blank" class="footer-link">粤ICP备20240001号-1</a>
                <span class="separator">|</span>
                <a href="/privacy" class="footer-link">隐私政策</a>
            </div>
        </div>
    </div>
</footer>

<!-- 返回顶部按钮 -->
<div id="back-to-top" class="back-to-top">
    <i class="fa fa-chevron-up"></i>
</div>

<style>
.footer {
    background: #f8f9fa;
    color: #6c757d;
    padding: 20px 0;
    margin-top: 40px;
    border-top: 1px solid #e9ecef;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.copyright {
    color: #6c757d;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 12px;
}

.footer-link {
    color: #6c757d;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #A67B5B;
}

.separator {
    color: #6c757d;
}

.back-to-top {
    position: fixed !important;
    bottom: 40px !important;
    right: 20px !important;
    width: 50px !important;
    height: 50px !important;
    background: #A67B5B !important;
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
    box-shadow: 0 4px 12px rgba(166,123,91,0.3) !important;
}

.back-to-top.show {
    opacity: 1 !important;
    visibility: visible !important;
    display: flex !important;
}

.back-to-top:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(166,123,91,0.4) !important;
    background: #8B4513 !important;
}

.back-to-top i {
    font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .back-to-top {
        bottom: 30px !important;
        right: 15px !important;
        width: 45px !important;
        height: 45px !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // 返回顶部功能
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $('#back-to-top').addClass('show');
        } else {
            $('#back-to-top').removeClass('show');
        }
    });

    $('#back-to-top').click(function() {
        $('html, body').animate({
            scrollTop: 0
        }, 800);
    });
});
</script> 

    <style>
        .header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            position: relative;
            z-index: 1000;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            color: #8B4513;
            flex-shrink: 0;
        }

        .logo:hover {
            color: #8B4513;
            text-decoration: none;
        }

        .logo-icon {
            font-size: 24px;
            color: #8B4513;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
            color: #8B4513;
            margin: 0;
        }

        .logo-subtitle {
            font-size: 12px;
            color: #A67B5B;
            margin: 0;
            font-weight: normal;
        }

        .search-section {
            flex: 1;
            max-width: 500px;
            margin: 0 20px;
        }

        .search-box {
            display: flex;
            align-items: center;
            background: #f8f8f8;
            border: 1px solid #e0e0e0;
            border-radius: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
            width: 100%;
            height: 44px;
        }

        .search-box:focus-within {
            border-color: #8B4513;
            box-shadow: 0 0 0 3px rgba(139,69,19,0.1);
        }

        .search-input {
            flex: 1;
            padding: 10px 15px;
            border: none;
            background: transparent;
            color: #333;
            font-size: 14px;
            outline: none;
            height: 100%;
            box-sizing: border-box;
        }

        .search-input::placeholder {
            color: #999;
        }

        .search-btn {
            padding: 10px 20px;
            background: #8B4513;
            border: none;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 45px;
            height: 100%;
            box-sizing: border-box;
        }

        .search-btn i {
            font-size: 16px;
        }

        .search-btn:hover {
            background: #A67B5B;
        }



        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-shrink: 0;
        }

        .system-info {
            text-align: right;
        }

        .info-text {
            color: #A67B5B;
            font-size: 12px;
            font-style: italic;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #333;
            position: relative;
            cursor: pointer;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #8B4513;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(139,69,19,0.2);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: none;
        }

        .avatar-icon {
            font-size: 14px;
            color: #8B4513;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin: 0;
        }

        .user-status {
            font-size: 11px;
            color: #A67B5B;
            margin: 0;
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 10000;
            margin-top: 8px;
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 14px;
            cursor: pointer;
        }

        .dropdown-item:hover {
            background: #f8f8f8;
            color: #8B4513;
            text-decoration: none;
        }

        .dropdown-item:focus {
            text-decoration: none;
        }

        .dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }

        .dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }

        .dropdown-icon {
            font-size: 14px;
            color: #8B4513;
            width: 16px;
            text-align: center;
        }

        /* 手机端汉堡菜单 */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            justify-content: space-around;
            width: 25px;
            height: 25px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
            z-index: 1001;
        }

        /* 确保初始状态正确 */
        .search-section {
            display: flex;
        }

        .user-info {
            display: flex;
        }

        .mobile-menu-toggle span {
            width: 100%;
            height: 2px;
            background: #8B4513;
            border-radius: 1px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        /* 手机端展开菜单 */
        .mobile-menu {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }

        .mobile-menu.show {
            display: block;
        }

        .mobile-menu-content {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #fff;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            padding-top: 60px;
        }

        .mobile-menu.show .mobile-menu-content {
            transform: translateY(0);
        }

        .mobile-menu-header {
            padding: 30px 20px 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f8f8;
            text-align: center;
        }



        .mobile-user-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 20px;
            background: #fff;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            max-width: 90%;
            margin-left: auto;
            margin-right: auto;
        }

        .mobile-user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #8B4513;
            flex-shrink: 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .mobile-user-avatar:hover {
            border-color: #A67B5B;
            transform: scale(1.05);
        }
        
        .mobile-user-avatar i {
            font-size: 40px;
            color: #A67B5B;
        }

        .mobile-user-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: none;
        }

        .mobile-user-details h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
            color: #333;
            font-weight: 600;
            text-align: center;
        }

        .mobile-user-details p {
            margin: 0;
            font-size: 16px;
            color: #8B4513;
            background: #f8f8f8;
            padding: 6px 12px;
            border-radius: 6px;
            display: inline-block;
            text-align: center;
        }

        .mobile-menu-nav {
            padding: 20px;
            max-width: 100%;
            margin: 0 auto;
        }

        .mobile-nav-section {
            margin-bottom: 30px;
        }

        .mobile-nav-title {
            font-size: 16px;
            font-weight: 600;
            color: #8B4513;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #8B4513;
            text-align: left;
        }

        .mobile-nav-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 18px 0;
            color: #333;
            text-decoration: none;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .mobile-nav-item:focus {
            text-decoration: none;
        }

        .mobile-nav-item:hover {
            color: #8B4513;
            background: #f8f8f8;
            margin: 0 -10px;
            padding: 18px 10px;
            text-decoration: none;
        }

        .mobile-nav-item:last-child {
            border-bottom: none;
        }

        .mobile-nav-icon {
            font-size: 18px;
            color: #8B4513;
            width: 24px;
            text-align: center;
        }

        .mobile-nav-text {
            font-size: 16px;
            font-weight: 500;
        }

        /* 手机端搜索栏样式 */
        .mobile-search-box {
            display: flex;
            align-items: center;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-top: 12px;
            width: 100%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 50px;
        }

        .mobile-search-box:focus-within {
            border-color: #8B4513;
            box-shadow: 0 0 0 3px rgba(139,69,19,0.1);
        }

        .mobile-search-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            color: #333;
            font-size: 16px;
            outline: none;
            height: 100%;
            box-sizing: border-box;
        }

        .mobile-search-input::placeholder {
            color: #999;
        }

        .mobile-search-btn {
            padding: 15px 18px;
            background: #8B4513;
            border: none;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 55px;
            border-radius: 0 25px 25px 0;
            height: 100%;
            box-sizing: border-box;
        }

        .mobile-search-btn:hover {
            background: #A67B5B;
        }

        .mobile-search-btn i {
            font-size: 18px;
            color: #fff;
        }



        @media (max-width: 768px) {
            .header-container {
                padding: 8px 12px;
                gap: 8px;
                flex-wrap: nowrap;
                align-items: center;
            }
            
            .logo {
                flex-shrink: 0;
                min-width: auto;
            }
            
            .logo-text {
                font-size: 14px;
            }
            
            .logo-subtitle {
                font-size: 10px;
                display: none;
            }
            
            /* 手机端隐藏搜索栏 */
            .search-section {
                display: none;
            }
            
            /* 手机端隐藏用户信息 */
            .user-info {
                display: none;
            }
            
            .system-info {
                display: none;
            }
            
            /* 显示汉堡菜单 */
            .mobile-menu-toggle {
                display: flex;
            }

            /* 隐藏PC端下拉菜单 */
            .user-dropdown {
                display: none;
            }
            
            /* 如果搜索栏显示，设置高度 */
            .search-box {
                height: 40px;
            }
        }
        
        @media (max-width: 480px) {
            .header-container {
                padding: 6px 8px;
                gap: 6px;
            }
            
            .logo-text {
                font-size: 12px;
            }
            
            .search-input {
                font-size: 11px;
                padding: 4px 6px;
            }
            
            .search-btn {
                padding: 4px 6px;
                min-width: 28px;
            }
            
            .search-box {
                height: 32px;
            }
            
            .user-name {
                font-size: 11px;
            }
            
            .user-status {
                font-size: 9px;
            }
            
            .user-avatar {
                width: 24px;
                height: 24px;
                font-size: 10px;
            }
            
            .user-avatar img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
            }
        }
        
        @media (max-width: 360px) {
            .header-container {
                padding: 4px 6px;
                gap: 4px;
            }
            
            .logo-text {
                font-size: 11px;
            }
            
            .search-input {
                font-size: 10px;
                padding: 3px 4px;
            }
            
            .search-btn {
                padding: 3px 4px;
                min-width: 24px;
            }
            
            .search-box {
                height: 28px;
            }
            
            .user-name {
                font-size: 10px;
            }
            
            .user-status {
                font-size: 8px;
            }
            
            .user-avatar {
                width: 20px;
                height: 20px;
                font-size: 8px;
            }
            
            .user-avatar img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
            }
        }
        
        /* 未登录状态样式 */
        .user-info .user-avatar i {
            font-size: 40px;
            color: #A67B5B;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .user-info .user-avatar i:hover {
            color: #8B4513;
        }
        
        .user-info .user-details .user-name {
            color: #6c757d;
        }
        
        .user-info .user-details .user-status {
            color: #A67B5B;
            font-weight: 500;
        }
    </style>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-container">
            <a href="/" class="logo">
                <div class="logo-icon">☸</div>
                <div>
                    <div class="logo-text">佛经智慧</div>
                    <div class="logo-subtitle">传承千年佛法 · 开启智慧人生</div>
                </div>
            </a>
            
            <div class="search-section">
                <div class="search-box">
                    <input type="text" placeholder="搜索佛经文章..." class="search-input">
                    <button class="search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
            
            <div class="user-section">
                <div class="system-info">
                    <span class="info-text">智慧传承 · 心灵净化</span>
                </div>
                
                <div class="user-info">
                    <div class="user-avatar" onclick="toggleUserMenu()">
                        <i class="fa fa-user avatar-icon"></i>
                    </div>
                    <div class="user-details" onclick="toggleUserMenu()">
                        <div class="user-name">微信用户</div>
                        <div class="user-status">专家用户</div>
                    </div>
                    <div class="user-dropdown">
                        <a href="/my-annotations" class="dropdown-item">
                            <i class="fa fa-edit dropdown-icon"></i>
                            <span>我的批注</span>
                        </a>
                        <a href="/my-comments" class="dropdown-item">
                            <i class="fa fa-comments dropdown-icon"></i>
                            <span>我的评论</span>
                        </a>
                        <a href="#" class="dropdown-item" onclick="logoutWechat()">
                            <i class="fa fa-sign-out dropdown-icon"></i>
                            <span>退出微信授权</span>
                        </a>
                    </div>
                </div>

                <!-- 手机端汉堡菜单 -->
                <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- 手机端展开菜单 -->
    <div class="mobile-menu" id="mobileMenu">
        <div class="mobile-menu-content">
            <div class="mobile-menu-header">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <i class="fa fa-user avatar-icon"></i>
                    </div>
                    <div class="mobile-user-details">
                        <h3>微信用户</h3>
                        <p>专家用户</p>
                    </div>
                </div>
            </div>
            
            <div class="mobile-menu-nav">
                <!-- 手机端搜索栏 -->
                <div class="mobile-nav-section">
                    <div class="mobile-nav-title">搜索佛经</div>
                    <div class="mobile-search-box">
                        <input type="text" placeholder="搜索佛经文章..." class="mobile-search-input">
                        <button class="mobile-search-btn">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mobile-nav-section">
                    <div class="mobile-nav-title">我的功能</div>
                    <a href="/my-annotations" class="mobile-nav-item">
                        <i class="fa fa-edit mobile-nav-icon"></i>
                        <span class="mobile-nav-text">我的批注</span>
                    </a>
                    <a href="/my-comments" class="mobile-nav-item">
                        <i class="fa fa-comments mobile-nav-icon"></i>
                        <span class="mobile-nav-text">我的评论</span>
                    </a>
                    <a href="#" class="mobile-nav-item" onclick="logoutWechat()">
                        <i class="fa fa-sign-out mobile-nav-icon"></i>
                        <span class="mobile-nav-text">退出微信授权</span>
                    </a>
                </div>
            </div>
            

        </div>
    </div>

    <script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
    <script src="/assets/libs/fastadmin-layer/dist/layer.js"></script>
    <link rel="stylesheet" href="/assets/libs/fastadmin-layer/dist/theme/default/layer.css">
    <!-- 引入API统一管理工具 -->
    <script src="/assets/js/frontend/api.js"></script>
    <link rel="stylesheet" href="/assets/libs/font-awesome/css/font-awesome.min.css">

    <script>


        // 手机端菜单切换
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            const menuToggle = document.querySelector('.mobile-menu-toggle');
            
            if (mobileMenu.classList.contains('show')) {
                mobileMenu.classList.remove('show');
                menuToggle.classList.remove('active');
                document.body.style.overflow = 'auto';
            } else {
                mobileMenu.classList.add('show');
                menuToggle.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        // 点击遮罩关闭菜单
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenu = document.getElementById('mobileMenu');
            
            mobileMenu.addEventListener('click', function(e) {
                if (e.target === mobileMenu) {
                    toggleMobileMenu();
                }
            });
        });

        // PC端用户菜单切换
        let hideTimeout;
        
        function toggleUserMenu() {
            const dropdown = document.querySelector('.user-dropdown');
            if (dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
            } else {
                dropdown.classList.add('show');
            }
        }

        // 鼠标悬停显示菜单
        document.addEventListener('DOMContentLoaded', function() {
            const userInfo = document.querySelector('.user-info');
            const userDropdown = document.querySelector('.user-dropdown');
            
            if (userInfo && userDropdown) {
                userInfo.addEventListener('mouseenter', function() {
                    clearTimeout(hideTimeout);
                    userDropdown.classList.add('show');
                });
                
                userInfo.addEventListener('mouseleave', function() {
                    hideTimeout = setTimeout(function() {
                        userDropdown.classList.remove('show');
                    }, 300);
                });
                
                userDropdown.addEventListener('mouseenter', function() {
                    clearTimeout(hideTimeout);
                });
                
                userDropdown.addEventListener('mouseleave', function() {
                    hideTimeout = setTimeout(function() {
                        userDropdown.classList.remove('show');
                    }, 100);
                });
            }
        });

        // 微信退出授权
        async function logoutWechat() {
            try {
                // 使用API统一管理工具调用后端退出接口
                const data = await API.post('/api/wechat/logout');
                
                if (data.code === 1) {
                    // 清除前端用户状态和token
                    currentUser = null;
                    userToken = null;
                    localStorage.removeItem('user_token');
                    sessionStorage.removeItem('user_token');
                    updateUserStatus(false);
                    
                    // 显示退出成功提示
                    if (typeof layer !== 'undefined') {
                        layer.msg('退出成功', {icon: 1});
                    } else {
                        alert('退出成功');
                    }
                    
                    // 关闭手机端菜单
                    if (window.innerWidth <= 768) {
                        toggleMobileMenu();
                    }
                } else {
                    if (typeof layer !== 'undefined') {
                        layer.msg(data.msg || '退出失败', {icon: 2});
                    } else {
                        alert(data.msg || '退出失败');
                    }
                }
            } catch (error) {
                console.error('退出失败:', error);
                // 即使接口失败，也清除前端状态
                currentUser = null;
                userToken = null;
                localStorage.removeItem('user_token');
                sessionStorage.removeItem('user_token');
                updateUserStatus(false);
                
                if (typeof layer !== 'undefined') {
                    layer.msg('退出失败', {icon: 2});
                } else {
                    alert('退出失败');
                }
            }
        }
        
        // 显示登录二维码
        function showLoginQrCode() {
            // 保存当前滚动位置并禁用背景滚动
            const scrollY = window.scrollY;
            document.body.style.position = 'fixed';
            document.body.style.top = `-${scrollY}px`;
            document.body.style.width = '100%';
            document.body.style.overflow = 'hidden';
            
            // 创建登录二维码弹窗
            const loginModal = document.createElement('div');
            loginModal.className = 'login-qr-modal';
            loginModal.innerHTML = `
                <div class="login-qr-modal-content">
                    <div class="login-qr-modal-header">
                        <h3>微信扫码登录</h3>
                        <button class="login-qr-close-btn" onclick="closeLoginQrModal()">×</button>
                    </div>
                    <div class="login-qr-modal-body">
                        <div class="login-qr-container">
                            <div class="login-qr-code privacy-blur">
                                <i class="fa fa-qrcode"></i>
                                <div class="login-qr-text">微信登录二维码</div>
                                <div class="privacy-overlay">
                                    <i class="fa fa-lock"></i>
                                    <p>请先同意隐私政策</p>
                                </div>
                            </div>
                            <p class="login-qr-tip">请使用微信扫描二维码登录</p>
                            <div class="login-qr-status">
                                <i class="fa fa-exclamation-triangle"></i>
                                <span>请先阅读并同意隐私政策</span>
                            </div>
                        </div>
                        
                        <!-- 隐私政策勾选 -->
                        <div class="privacy-checkbox-container">
                            <label class="privacy-checkbox">
                                <input type="checkbox" id="privacyCheckbox" class="privacy-input" onchange="handlePrivacyCheckbox()">
                                <span class="privacy-checkmark"></span>
                                <span class="privacy-text">
                                    我已阅读并同意
                                    <a href="javascript:void(0)" class="privacy-link" onclick="openPrivacyPage()">《隐私政策》</a>
                                </span>
                            </label>
                        </div>
                        
                        <div class="login-qr-actions">
                            <button class="login-qr-btn login-qr-btn-refresh" onclick="refreshQrCode()">
                                <i class="fa fa-refresh"></i>
                                刷新二维码
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(loginModal);
            
            // 添加登录二维码样式
            if (!document.getElementById('login-qr-modal-styles')) {
                const style = document.createElement('style');
                style.id = 'login-qr-modal-styles';
                style.textContent = `
                    .login-qr-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 99999;
                        animation: fadeIn 0.3s ease;
                    }
                    
                    .login-qr-modal-content {
                        background: white;
                        border-radius: 12px;
                        width: 90%;
                        max-width: 400px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                        animation: slideIn 0.3s ease;
                    }
                    
                    .login-qr-modal-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 20px 24px 0;
                        border-bottom: 1px solid #e9ecef;
                        padding-bottom: 15px;
                    }
                    
                    .login-qr-modal-header h3 {
                        margin: 0;
                        font-size: 18px;
                        color: #333;
                        font-weight: 600;
                    }
                    
                    .login-qr-close-btn {
                        background: none;
                        border: none;
                        font-size: 24px;
                        color: #999;
                        cursor: pointer;
                        padding: 0;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: all 0.3s ease;
                    }
                    
                    .login-qr-close-btn:hover {
                        background: #f5f5f5;
                        color: #666;
                    }
                    
                    .login-qr-modal-body {
                        padding: 24px;
                    }
                    
                    .login-qr-container {
                        text-align: center;
                        margin-bottom: 24px;
                    }
                    
                    .login-qr-code {
                        width: 200px;
                        height: 200px;
                        background: #f8f9fa;
                        border: 2px dashed #dee2e6;
                        border-radius: 12px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;
                        color: #6c757d;
                        position: relative;
                    }
                    
                    .login-qr-code i {
                        font-size: 80px;
                        margin-bottom: 8px;
                        color: #A67B5B;
                    }
                    
                    .login-qr-text {
                        font-size: 14px;
                        color: #6c757d;
                    }
                    
                    .login-qr-tip {
                        color: #6c757d;
                        font-size: 14px;
                        margin: 0 0 12px 0;
                        line-height: 1.5;
                    }
                    
                    .login-qr-status {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 8px;
                        color: #A67B5B;
                        font-size: 14px;
                        margin-bottom: 16px;
                    }
                    
                    .login-qr-status i {
                        font-size: 16px;
                    }
                    
                    .login-qr-actions {
                        display: flex;
                        gap: 12px;
                        justify-content: center;
                    }
                    
                    .login-qr-btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        font-size: 14px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                    }
                    
                    .login-qr-btn-refresh {
                        background: #A67B5B;
                        color: white;
                    }
                    
                    .login-qr-btn-refresh:hover {
                        background: #8B4513;
                    }
                    
                    .login-qr-btn-cancel {
                        background: #f8f9fa;
                        color: #6c757d;
                        border: 1px solid #dee2e6;
                    }
                    
                    .login-qr-btn-cancel:hover {
                        background: #e9ecef;
                        color: #495057;
                    }
                    
                    /* 隐私政策相关样式 */
                    .privacy-blur {
                        filter: blur(3px);
                        position: relative;
                    }
                    
                    .privacy-overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.7);
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        border-radius: 12px;
                    }
                    
                    .privacy-overlay i {
                        font-size: 24px;
                        margin-bottom: 8px;
                        color: #ff6b6b;
                    }
                    
                    .privacy-overlay p {
                        margin: 0;
                        font-size: 14px;
                        font-weight: 500;
                    }
                    
                    /* 隐私政策勾选框样式 */
                    .privacy-checkbox-container {
                        margin: 20px 0;
                        text-align: center;
                    }
                    
                    .privacy-checkbox {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 8px;
                        cursor: pointer;
                        font-size: 14px;
                        color: #6c757d;
                    }
                    
                    .privacy-input {
                        display: none;
                    }
                    
                    .privacy-checkmark {
                        width: 18px;
                        height: 18px;
                        border: 2px solid #dee2e6;
                        border-radius: 3px;
                        background: white;
                        position: relative;
                        transition: all 0.3s ease;
                        flex-shrink: 0;
                    }
                    
                    .privacy-checkbox:hover .privacy-checkmark {
                        border-color: #A67B5B;
                    }
                    
                    .privacy-input:checked + .privacy-checkmark {
                        background: #A67B5B;
                        border-color: #A67B5B;
                    }
                    
                    .privacy-input:checked + .privacy-checkmark::after {
                        content: '✓';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: white;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    
                    .privacy-text {
                        font-size: 14px;
                        line-height: 1.4;
                    }
                    
                    .privacy-link {
                        color: #A67B5B;
                        text-decoration: none;
                        font-weight: 500;
                        transition: color 0.3s ease;
                    }
                    
                    .privacy-link:hover {
                        color: #8B4513;
                        text-decoration: underline;
                    }
                    
                    @media (max-width: 480px) {
                        .login-qr-modal-content {
                            width: 95%;
                            margin: 20px;
                        }
                        
                        .login-qr-code {
                            width: 160px;
                            height: 160px;
                        }
                        
                        .login-qr-code i {
                            font-size: 60px;
                        }
                        
                        .login-qr-actions {
                            flex-direction: column;
                        }
                        
                        .login-qr-btn {
                            width: 100%;
                            justify-content: center;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
            
            // 二维码加载与轮询由 handlePrivacyCheckbox 在“已同意且已勾选”后触发
            

            
            // 点击遮罩关闭弹窗
            loginModal.addEventListener('click', function(e) {
                if (e.target === loginModal) {
                    closeLoginQrModal();
                }
            });
            
            // ESC键关闭弹窗
            const handleEscKey = function(e) {
                if (e.key === 'Escape') {
                    closeLoginQrModal();
                    document.removeEventListener('keydown', handleEscKey);
                }
            };
            document.addEventListener('keydown', handleEscKey);
            
            // 开发模式下添加“模拟扫码确认”按钮
            if (isDev()) {
                const actions = loginModal.querySelector('.login-qr-actions');
                if (actions) {
                    const devBtn = document.createElement('button');
                    devBtn.className = 'login-qr-btn login-qr-btn-refresh';
                    devBtn.type = 'button';
                    devBtn.innerHTML = '<i class="fa fa-magic"></i> 模拟扫码确认';
                    devBtn.addEventListener('click', devSimulateScan);
                    actions.appendChild(devBtn);
                }
            }
        }
        
        // 关闭登录二维码弹窗
        function closeLoginQrModal() {
            const modal = document.querySelector('.login-qr-modal');
            if (modal) {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    if (modal.parentNode) {
                        modal.parentNode.removeChild(modal);
                    }
                    // 恢复背景滚动和位置
                    const scrollY = document.body.style.top;
                    document.body.style.position = '';
                    document.body.style.top = '';
                    document.body.style.width = '';
                    document.body.style.overflow = '';
                    window.scrollTo(0, parseInt(scrollY || '0') * -1);
                }, 300);
            } else {
                // 如果没有找到弹窗，也要恢复背景滚动
                const scrollY = document.body.style.top;
                document.body.style.position = '';
                document.body.style.top = '';
                document.body.style.width = '';
                document.body.style.overflow = '';
                window.scrollTo(0, parseInt(scrollY || '0') * -1);
            }
        }
        
        // 检查隐私政策是否已同意
        function checkPrivacyAgreed() {
            return localStorage.getItem('privacy_agreed') === 'true';
        }
        
        // 初始化隐私政策检查
        function initializePrivacyCheck() {
            // 检查localStorage中的同意状态
            if (checkPrivacyAgreed()) {
                // 如果已经同意过，启用复选框但不自动勾选
                const privacyCheckbox = document.getElementById('privacyCheckbox');
                if (privacyCheckbox) {
                    privacyCheckbox.checked = false; // 不自动勾选
                    privacyCheckbox.disabled = false; // 启用复选框
                }
                // 不移除模糊，提示用户勾选复选框
                const status = document.querySelector('.login-qr-status');
                if (status) {
                    status.innerHTML = '<i class="fa fa-exclamation-triangle"></i><span>请勾选复选框以显示二维码</span>';
                }
            } else {
                // 如果没有同意过，禁用复选框
                const privacyCheckbox = document.getElementById('privacyCheckbox');
                if (privacyCheckbox) {
                    privacyCheckbox.checked = false;
                    privacyCheckbox.disabled = true;
                }
            }
        }
        
        // 处理隐私政策勾选
        function handlePrivacyCheckbox() {
            const privacyCheckbox = document.getElementById('privacyCheckbox');
            const qrCode = document.querySelector('.login-qr-code');
            const status = document.querySelector('.login-qr-status');
            
            // 先检查localStorage中的同意状态
            if (checkPrivacyAgreed()) {
                // 如果已经同意过，根据勾选状态处理
                if (privacyCheckbox && privacyCheckbox.checked) {
                    // 用户手动勾选了，清除模糊效果
                    if (qrCode) {
                        qrCode.classList.remove('privacy-blur');
                        const overlay = qrCode.querySelector('.privacy-overlay');
                        if (overlay) {
                            overlay.remove();
                        }
                        // 勾选后触发二维码初始化与轮询
                        initWxQrAndPoll();
                    }
                    
                    if (status) {
                        status.innerHTML = '<i class="fa fa-clock-o"></i><span>等待扫码中...</span>';
                    }
                } else {
                    // 用户取消勾选了，恢复模糊效果
                    if (qrCode) {
                        qrCode.classList.add('privacy-blur');
                        if (!qrCode.querySelector('.privacy-overlay')) {
                            const overlay = document.createElement('div');
                            overlay.className = 'privacy-overlay';
                            overlay.innerHTML = `
                                <i class="fa fa-lock"></i>
                                <p>请先同意隐私政策</p>
                            `;
                            qrCode.appendChild(overlay);
                        }
                    }
                    
                    if (status) {
                        status.innerHTML = '<i class="fa fa-exclamation-triangle"></i><span>请先阅读并同意隐私政策</span>';
                    }
                }
                return;
            }
            
            // 如果没有同意过，不允许勾选
            if (privacyCheckbox) {
                privacyCheckbox.checked = false;
                privacyCheckbox.disabled = true;
            }
            
            // 显示提示信息
            if (qrCode) {
                qrCode.classList.add('privacy-blur');
                if (!qrCode.querySelector('.privacy-overlay')) {
                    const overlay = document.createElement('div');
                    overlay.className = 'privacy-overlay';
                    overlay.innerHTML = `
                        <i class="fa fa-lock"></i>
                        <p>请先同意隐私政策</p>
                    `;
                    qrCode.appendChild(overlay);
                }
            }
            
            if (status) {
                status.innerHTML = '<i class="fa fa-exclamation-triangle"></i><span>请先阅读并同意隐私政策</span>';
            }
            
            // 提示用户需要先同意隐私政策
            if (typeof layer !== 'undefined') {
                layer.msg('请先阅读并同意隐私政策', {icon: 2});
            } else {
                alert('请先阅读并同意隐私政策');
            }
        }
        
                // 打开隐私政策页面
        function openPrivacyPage() {
            // 在新窗口打开隐私政策页面
            window.open('/privacy', '_blank');
        }
        
        // 开发环境判断（用于显示模拟按钮）
        function isDev() {
            const host = location.hostname;
            if (host === 'localhost' || host === '127.0.0.1') return true;
            if (/dev=1/.test(location.search)) return true;
            return false;
        }
        
        // 微信登录全局对象
        let wxLogin = { state: null, timer: null };
        
        async function initWxQrAndPoll() {
            try {
                const json = await API.get('/api/wechat/login');
                if (json.code !== 1 || !json.data) throw new Error(json.msg || '获取登录态失败');
                wxLogin.state = json.data.state;
                // 暴露给控制台/外部工具，便于在未接入appid前手动模拟
                window.wxState = wxLogin.state;

                // 显示二维码图片
                const qrBox = document.querySelector('.login-qr-code');
                if (qrBox) {
                    // 同步state到DOM便于手动取用
                    qrBox.setAttribute('data-state', wxLogin.state);
                    let img = qrBox.querySelector('img.wx-qr-img');
                    if (!img) {
                        img = document.createElement('img');
                        img.className = 'wx-qr-img';
                        img.style.width = '100%';
                        img.style.height = '100%';
                        img.style.objectFit = 'cover';
                        // 插在二维码容器最上方（在文字前）
                        qrBox.insertBefore(img, qrBox.firstChild);
                    }
                    img.src = json.data.qrcode_url;
                }

                startWxStatusPolling();
            } catch (e) {
                const status = document.querySelector('.login-qr-status');
                if (status) {
                    status.innerHTML = '<i class="fa fa-exclamation-triangle"></i><span>获取二维码失败</span>';
                }
                console.error('initWxQrAndPoll failed:', e);
            }
        }
        
        function startWxStatusPolling() {
            stopWxStatusPolling();
            if (!wxLogin.state) return;
            wxLogin.timer = setInterval(async () => {
                try {
                    // 若未勾选，停止轮询，避免无意义请求
                    const cb = document.getElementById('privacyCheckbox');
                    if (!cb || !cb.checked) {
                        stopWxStatusPolling();
                        return;
                    }
                    const j = await API.get('/api/wechat/status', { state: wxLogin.state });
                    if (j.code !== 1) return;
                    if (j.data.status === 'success') {
                        stopWxStatusPolling();
                        closeLoginQrModal();
                        
                        // 保存token（如果轮询返回了token）
                        if (j.data.token) {
                            userToken = j.data.token;
                            try {
                                localStorage.setItem('user_token', userToken);
                                console.log('轮询成功，Token已保存到localStorage:', userToken);
                            } catch (error) {
                                console.error('localStorage保存失败:', error);
                                sessionStorage.setItem('user_token', userToken);
                                console.log('轮询成功，Token已保存到sessionStorage:', userToken);
                            }
                        }
                        
                        // 轮询成功时也检查用户状态
                        const loginSuccess = await checkUserLoginStatus();
                        if (loginSuccess) {
                            if (typeof layer !== 'undefined') {
                                layer.msg('登录成功', {icon: 1});
                            }
                        } else {
                            if (typeof layer !== 'undefined') {
                                layer.msg('登录成功但获取用户信息失败', {icon: 2});
                            }
                        }
                    } else if (j.data.status === 'expired') {
                        const status = document.querySelector('.login-qr-status');
                        if (status) {
                            status.innerHTML = '<i class="fa fa-exclamation-triangle"></i><span>二维码已过期，请刷新</span>';
                        }
                        stopWxStatusPolling();
                    }
                } catch (e) {
                    // 忽略临时错误
                }
            }, 2000);
        }
        
        function stopWxStatusPolling() {
            if (wxLogin.timer) {
                clearInterval(wxLogin.timer);
                wxLogin.timer = null;
            }
        }
        
        // 开发模式下：直接模拟扫码确认
        async function devSimulateScan() {
            try {
                const state = wxLogin.state || (document.querySelector('.login-qr-code')?.getAttribute('data-state'));
                if (!state) {
                    if (typeof layer !== 'undefined') layer.msg('未获取到state，请先勾选加载二维码', {icon: 2});
                    else alert('未获取到state，请先勾选加载二维码');
                    return;
                }
                // 前端强制：只有同意且勾选时才允许模拟
                if (localStorage.getItem('privacy_agreed') !== 'true') {
                    if (typeof layer !== 'undefined') layer.msg('必须先同意隐私政策', {icon: 2});
                    else alert('必须先同意隐私政策');
                    return;
                }
                const cb = document.getElementById('privacyCheckbox');
                if (!cb || !cb.checked) {
                    if (typeof layer !== 'undefined') layer.msg('请勾选隐私政策复选框', {icon: 2});
                    else alert('请勾选隐私政策复选框');
                    return;
                }
                const json = await API.post('/api/wechat/confirm', {
                    state: state,
                    nickname: '开发模拟用户',
                    avatar: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop',
                    privacy_agreed: '1'
                });
                console.log('模拟扫码响应:', json); // 调试日志
                
                if (json.code === 1) {
                    stopWxStatusPolling();
                    closeLoginQrModal();
                    
                    // 保存token
                    if (json.data && json.data.token) {
                        userToken = json.data.token;
                        try {
                            localStorage.setItem('user_token', userToken);
                            console.log('Token已保存到localStorage:', userToken);
                            
                            // 验证是否保存成功
                            const savedToken = localStorage.getItem('user_token');
                            console.log('验证保存的token:', savedToken);
                            
                            if (savedToken !== userToken) {
                                console.error('Token保存失败，尝试使用sessionStorage');
                                sessionStorage.setItem('user_token', userToken);
                            }
                        } catch (error) {
                            console.error('localStorage保存失败:', error);
                            // 尝试使用sessionStorage作为备选
                            sessionStorage.setItem('user_token', userToken);
                            console.log('Token已保存到sessionStorage:', userToken);
                        }
                    } else {
                        console.log('响应中没有token:', json.data);
                    }
                    
                    // 登录成功后检查用户状态
                    const loginSuccess = await checkUserLoginStatus();
                    if (loginSuccess) {
                        console.log('模拟登录成功，用户信息:', currentUser);
                        if (typeof layer !== 'undefined') layer.msg('模拟扫码成功', {icon: 1});
                    } else {
                        if (typeof layer !== 'undefined') layer.msg('登录成功但获取用户信息失败', {icon: 2});
                        else alert('登录成功但获取用户信息失败');
                    }
                } else {
                    if (typeof layer !== 'undefined') layer.msg(json.msg || '模拟失败', {icon: 2});
                    else alert(json.msg || '模拟失败');
                }
            } catch (e) {
                if (typeof layer !== 'undefined') layer.msg('请求失败', {icon: 2});
                else alert('请求失败');
            }
        }
        
        // 用户同意隐私政策后的处理
        function onPrivacyAgreed() {
            // 保存同意状态到localStorage
            localStorage.setItem('privacy_agreed', 'true');
            localStorage.setItem('privacy_agreed_time', new Date().toISOString());
            
            // 启用复选框但不自动勾选
            const privacyCheckbox = document.getElementById('privacyCheckbox');
            if (privacyCheckbox) {
                privacyCheckbox.disabled = false;
                privacyCheckbox.checked = false; // 不自动勾选
            }
            // 不移除模糊，提示用户勾选复选框
            const status = document.querySelector('.login-qr-status');
            if (status) {
                status.innerHTML = '<i class="fa fa-exclamation-triangle"></i><span>请勾选复选框以显示二维码</span>';
            }
            
            // 提示用户
            if (typeof layer !== 'undefined') {
                layer.msg('隐私政策已同意，现在可以勾选复选框使用功能', {icon: 1});
            } else {
                alert('隐私政策已同意，现在可以勾选复选框使用功能');
            }
        }
        
        // 用户不同意隐私政策后的处理
        function onPrivacyDisagreed() {
            // 保存不同意状态到localStorage
            localStorage.setItem('privacy_agreed', 'false');
            localStorage.setItem('privacy_disagreed_time', new Date().toISOString());
            
            // 禁用复选框
            const privacyCheckbox = document.getElementById('privacyCheckbox');
            if (privacyCheckbox) {
                privacyCheckbox.disabled = true;
                privacyCheckbox.checked = false;
            }
            
            // 恢复二维码模糊效果
            const qrCode = document.querySelector('.login-qr-code');
            if (qrCode) {
                qrCode.classList.add('privacy-blur');
                if (!qrCode.querySelector('.privacy-overlay')) {
                    const overlay = document.createElement('div');
                    overlay.className = 'privacy-overlay';
                    overlay.innerHTML = `
                        <i class="fa fa-lock"></i>
                        <p>请先同意隐私政策</p>
                    `;
                    qrCode.appendChild(overlay);
                }
            }
            
            // 更新状态显示
            const status = document.querySelector('.login-qr-status');
            if (status) {
                status.innerHTML = '<i class="fa fa-exclamation-triangle"></i><span>请先阅读并同意隐私政策</span>';
            }
            
            // 提示用户
            if (typeof layer !== 'undefined') {
                layer.msg('您需要同意隐私政策才能使用功能', {icon: 2});
            } else {
                alert('您需要同意隐私政策才能使用功能');
            }
        }
        
        // 刷新二维码（需要已同意且已勾选）
        function refreshQrCode() {
            const privacyCheckbox = document.getElementById('privacyCheckbox');
            const hasAgreed = checkPrivacyAgreed();
            const hasChecked = !!(privacyCheckbox && privacyCheckbox.checked);

            if (hasAgreed && hasChecked) {
                const status = document.querySelector('.login-qr-status');
                if (status) {
                    status.innerHTML = '<i class="fa fa-spinner fa-spin"></i><span>正在刷新...</span>';
                    setTimeout(() => {
                        status.innerHTML = '<i class="fa fa-check"></i><span>二维码已刷新</span>';
                    }, 1500);
                }
                return;
            }

            // 未满足条件时提示
            if (!hasAgreed) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请先阅读并同意隐私政策', {icon: 2});
                } else {
                    alert('请先阅读并同意隐私政策');
                }
                return;
            }

            if (!hasChecked) {
                if (typeof layer !== 'undefined') {
                    layer.msg('请先勾选复选框以显示二维码', {icon: 2});
                } else {
                    alert('请先勾选复选框以显示二维码');
                }
            }
        }
        
        // 手机端直接微信授权
        function directWechatAuth() {
            // 先检查localStorage中的同意状态
            if (checkPrivacyAgreed()) {
                // 如果已经同意过，直接进行微信授权
                showMobileWechatAuth();
                return;
            }
            
            // 如果没有同意过，显示手机端隐私政策确认弹窗
            const mobilePrivacyModal = document.createElement('div');
            mobilePrivacyModal.className = 'mobile-privacy-modal';
            mobilePrivacyModal.innerHTML = `
                <div class="mobile-privacy-content">
                    <div class="mobile-privacy-header">
                        <h3>隐私政策确认</h3>
                    </div>
                    <div class="mobile-privacy-body">
                        <div class="mobile-privacy-icon">
                            <i class="fa fa-shield"></i>
                        </div>
                        <p class="mobile-privacy-tip">在继续之前，请阅读并同意我们的隐私政策</p>
                        
                        <!-- 手机端隐私政策勾选 -->
                        <div class="mobile-privacy-checkbox-container">
                            <label class="mobile-privacy-checkbox">
                                <input type="checkbox" id="mobilePrivacyCheckbox" class="mobile-privacy-input" onchange="handleMobilePrivacyCheckbox()">
                                <span class="mobile-privacy-checkmark"></span>
                                <span class="mobile-privacy-text">
                                    我已阅读并同意
                                    <a href="javascript:void(0)" class="mobile-privacy-link" onclick="openPrivacyPage()">《隐私政策》</a>
                                </span>
                            </label>
                        </div>
                        
                        <div class="mobile-privacy-actions">
                            <button class="mobile-privacy-btn mobile-privacy-cancel" onclick="closeMobilePrivacyModal()">取消</button>
                            <button class="mobile-privacy-btn mobile-privacy-confirm" id="mobilePrivacyConfirmBtn" disabled onclick="confirmMobilePrivacy()">
                                确认授权
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(mobilePrivacyModal);
            
            // 添加触摸事件支持
            setTimeout(() => {
                const cancelBtn = document.querySelector('.mobile-privacy-cancel');
                const confirmBtn = document.getElementById('mobilePrivacyConfirmBtn');
                const checkbox = document.getElementById('mobilePrivacyCheckbox');
                
                if (cancelBtn) {
                    cancelBtn.addEventListener('touchstart', function(e) {
                        e.preventDefault();
                        closeMobilePrivacyModal();
                    }, { passive: false });
                    
                    cancelBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        closeMobilePrivacyModal();
                    });
                }
                
                if (confirmBtn) {
                    confirmBtn.addEventListener('touchstart', function(e) {
                        e.preventDefault();
                        if (!this.disabled) {
                            confirmMobilePrivacy();
                        }
                    }, { passive: false });
                    
                    confirmBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        if (!this.disabled) {
                            confirmMobilePrivacy();
                        }
                    });
                }
                
                if (checkbox) {
                    // 允许原生点击/触摸切换，避免阻止勾选
                    checkbox.addEventListener('change', function() {
                        handleMobilePrivacyCheckbox();
                    });
                }
            }, 100);
            
            // 添加手机端隐私政策弹窗样式
            if (!document.getElementById('mobile-privacy-modal-styles')) {
                const style = document.createElement('style');
                style.id = 'mobile-privacy-modal-styles';
                style.textContent = `
                    .mobile-privacy-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 99999;
                        animation: fadeIn 0.3s ease;
                        -webkit-tap-highlight-color: transparent;
                    }
                    
                    .mobile-privacy-content {
                        background: white;
                        border-radius: 12px;
                        width: 90%;
                        max-width: 350px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                        animation: slideIn 0.3s ease;
                    }
                    
                    .mobile-privacy-header {
                        text-align: center;
                        padding: 20px 24px 0;
                        border-bottom: 1px solid #e9ecef;
                        padding-bottom: 15px;
                    }
                    
                    .mobile-privacy-header h3 {
                        margin: 0;
                        font-size: 18px;
                        color: #333;
                        font-weight: 600;
                    }
                    
                    .mobile-privacy-body {
                        padding: 24px;
                        text-align: center;
                    }
                    
                    .mobile-privacy-icon {
                        width: 60px;
                        height: 60px;
                        background: #A67B5B;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;
                        color: white;
                    }
                    
                    .mobile-privacy-icon i {
                        font-size: 30px;
                    }
                    
                    .mobile-privacy-tip {
                        color: #6c757d;
                        font-size: 14px;
                        margin: 0 0 20px 0;
                        line-height: 1.5;
                    }
                    
                    /* 手机端隐私政策勾选框样式 */
                    .mobile-privacy-checkbox-container {
                        margin: 20px 0;
                        text-align: left;
                    }
                    
                    .mobile-privacy-checkbox {
                        display: flex;
                        align-items: flex-start;
                        gap: 10px;
                        cursor: pointer;
                        font-size: 14px;
                        color: #6c757d;
                        line-height: 1.4;
                    }
                    
                    .mobile-privacy-input {
                        display: none;
                    }
                    
                    .mobile-privacy-checkmark {
                        width: 20px;
                        height: 20px;
                        border: 2px solid #dee2e6;
                        border-radius: 3px;
                        background: white;
                        position: relative;
                        transition: all 0.3s ease;
                        flex-shrink: 0;
                        margin-top: 2px;
                    }
                    
                    .mobile-privacy-checkbox:hover .mobile-privacy-checkmark {
                        border-color: #A67B5B;
                    }
                    
                    .mobile-privacy-input:checked + .mobile-privacy-checkmark {
                        background: #A67B5B;
                        border-color: #A67B5B;
                    }
                    
                    .mobile-privacy-input:checked + .mobile-privacy-checkmark::after {
                        content: '✓';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: white;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    
                    .mobile-privacy-text {
                        font-size: 14px;
                        line-height: 1.4;
                        flex: 1;
                    }
                    
                    .mobile-privacy-link {
                        color: #A67B5B;
                        text-decoration: none;
                        font-weight: 500;
                        transition: color 0.3s ease;
                    }
                    
                    .mobile-privacy-link:hover {
                        color: #8B4513;
                        text-decoration: underline;
                    }
                    
                    .mobile-privacy-actions {
                        display: flex;
                        gap: 12px;
                        margin-top: 24px;
                    }
                    
                    .mobile-privacy-btn {
                        flex: 1;
                        padding: 12px 20px;
                        border: none;
                        border-radius: 6px;
                        font-size: 14px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        -webkit-tap-highlight-color: transparent;
                        -webkit-touch-callout: none;
                        -webkit-user-select: none;
                        -moz-user-select: none;
                        -ms-user-select: none;
                        user-select: none;
                        touch-action: manipulation;
                    }
                    
                    .mobile-privacy-cancel {
                        background: #f8f9fa;
                        color: #6c757d;
                        border: 1px solid #dee2e6;
                    }
                    
                    .mobile-privacy-cancel:hover {
                        background: #e9ecef;
                        color: #495057;
                    }
                    
                    .mobile-privacy-confirm {
                        background: #A67B5B;
                        color: white;
                    }
                    
                    .mobile-privacy-confirm:hover:not(:disabled) {
                        background: #8B4513;
                    }
                    
                    .mobile-privacy-confirm:disabled {
                        background: #ccc;
                        cursor: not-allowed;
                    }
                `;
                document.head.appendChild(style);
            }
            
            // 手机端隐私政策勾选处理（仅控制确认按钮状态，不限制勾选）
            window.handleMobilePrivacyCheckbox = function() {
                const privacyCheckbox = document.getElementById('mobilePrivacyCheckbox');
                const confirmBtn = document.getElementById('mobilePrivacyConfirmBtn');
                if (!privacyCheckbox || !confirmBtn) return;

                if (privacyCheckbox.checked) {
                    confirmBtn.disabled = false;
                    confirmBtn.style.background = '#A67B5B';
                } else {
                    confirmBtn.disabled = true;
                    confirmBtn.style.background = '#ccc';
                }
            }
            
            // 确认手机端隐私政策
            window.confirmMobilePrivacy = function() {
                // 先检查localStorage中的同意状态
                if (checkPrivacyAgreed()) {
                    // 如果已经同意过，直接进行微信授权
                    closeMobilePrivacyModal();
                    showMobileWechatAuth();
                    return;
                }
                
                // 如果没有同意过，检查勾选框
                const privacyCheckbox = document.getElementById('mobilePrivacyCheckbox');
                if (!privacyCheckbox || !privacyCheckbox.checked) {
                    if (typeof layer !== 'undefined') {
                        layer.msg('请先阅读并同意隐私政策', {icon: 2});
                    } else {
                        alert('请先阅读并同意隐私政策');
                    }
                    return;
                }
                
                // 保存同意状态到localStorage
                localStorage.setItem('privacy_agreed', 'true');
                localStorage.setItem('privacy_agreed_time', new Date().toISOString());
                
                // 关闭隐私政策弹窗
                closeMobilePrivacyModal();
                
                // 显示微信授权弹窗
                showMobileWechatAuth();
            }
            
            // 显示手机端微信授权弹窗
            function showMobileWechatAuth() {
                const authModal = document.createElement('div');
                authModal.className = 'auth-modal';
                authModal.innerHTML = `
                    <div class="auth-modal-content">
                        <div class="auth-modal-header">
                            <h3>微信授权登录</h3>
                        </div>
                        <div class="auth-modal-body">
                            <div class="auth-container">
                                <div class="auth-icon">
                                    <i class="fa fa-wechat"></i>
                                </div>
                                <p class="auth-tip">正在跳转到微信授权...</p>
                                <div class="auth-status">
                                    <i class="fa fa-spinner fa-spin"></i>
                                    <span>授权中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(authModal);
                
                // 添加授权弹窗样式
                if (!document.getElementById('auth-modal-styles')) {
                    const style = document.createElement('style');
                    style.id = 'auth-modal-styles';
                    style.textContent = `
                        .auth-modal {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0, 0, 0, 0.5);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 99999;
                            animation: fadeIn 0.3s ease;
                        }
                        
                        .auth-modal-content {
                            background: white;
                            border-radius: 12px;
                            width: 90%;
                            max-width: 350px;
                            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                            animation: slideIn 0.3s ease;
                        }
                        
                        .auth-modal-header {
                            text-align: center;
                            padding: 20px 24px 0;
                            border-bottom: 1px solid #e9ecef;
                            padding-bottom: 15px;
                        }
                        
                        .auth-modal-header h3 {
                            margin: 0;
                            font-size: 18px;
                            color: #333;
                            font-weight: 600;
                        }
                        
                        .auth-modal-body {
                            padding: 24px;
                        }
                        
                        .auth-container {
                            text-align: center;
                        }
                        
                        .auth-icon {
                            width: 80px;
                            height: 80px;
                            background: #07C160;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 16px;
                            color: white;
                        }
                        
                        .auth-icon i {
                            font-size: 40px;
                        }
                        
                        .auth-tip {
                            color: #6c757d;
                            font-size: 14px;
                            margin: 0 0 12px 0;
                            line-height: 1.5;
                        }
                        
                        .auth-status {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 8px;
                            color: #07C160;
                            font-size: 14px;
                        }
                        
                        .auth-status i {
                            font-size: 16px;
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                // 模拟微信授权流程
                setTimeout(() => {
                    // 这里可以添加实际的微信授权逻辑
                    console.log('调用微信授权接口');
                    
                    // 模拟授权成功
                    setTimeout(() => {
                        closeAuthModal();
                        // 更新用户状态为已登录
                        updateUserStatus(true);
                        
                        if (typeof layer !== 'undefined') {
                            layer.msg('微信授权成功', {icon: 1});
                        } else {
                            alert('微信授权成功');
                        }
                    }, 2000);
                }, 1000);
            }
            
            // 关闭手机端隐私政策弹窗
            window.closeMobilePrivacyModal = function() {
                const modal = document.querySelector('.mobile-privacy-modal');
                if (modal) {
                    modal.style.animation = 'fadeOut 0.3s ease';
                    setTimeout(() => {
                        if (modal.parentNode) {
                            modal.parentNode.removeChild(modal);
                        }
                    }, 300);
                }
            }
            
            // 关闭授权弹窗
            function closeAuthModal() {
                const modal = document.querySelector('.auth-modal');
                if (modal) {
                    modal.style.animation = 'fadeOut 0.3s ease';
                    setTimeout(() => {
                        if (modal.parentNode) {
                            modal.parentNode.removeChild(modal);
                        }
                    }, 300);
                }
            }
        }
        
        // 关闭授权弹窗
        function closeAuthModal() {
            const modal = document.querySelector('.auth-modal');
            if (modal) {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    if (modal.parentNode) {
                        modal.parentNode.removeChild(modal);
                    }
                }, 300);
            }
        }
        
        // 统一登录处理函数
        function handleLogin() {
            // 检查是否为手机端
            if (window.innerWidth <= 768) {
                // 手机端直接调用微信授权
                directWechatAuth();
            } else {
                // PC端显示登录二维码
                showLoginQrCode();
            }
        }
        
        // 更新手机端菜单状态
        function updateMobileMenuStatus(isLoggedIn) {
            const mobileUserInfo = document.querySelector('.mobile-user-info');
            const mobileMenuNav = document.querySelector('.mobile-menu-nav');
            
            if (!mobileUserInfo || !mobileMenuNav) return;
            
            if (isLoggedIn && currentUser) {
                // 已登录状态
                const avatar = currentUser.avatar || 'https://via.placeholder.com/80x80/A67B5B/ffffff?text=微';
                const nickname = currentUser.nickname || '微信用户';
                const isExpert = currentUser.is_expert == 1;
                const isExpertText = isExpert ? '专家用户' : '普通用户';
                
                mobileUserInfo.innerHTML = `
                    <div class="mobile-user-avatar">
                        <img src="${avatar}" alt="用户头像" onerror="this.src='https://via.placeholder.com/80x80/A67B5B/ffffff?text=微'">
                    </div>
                    <div class="mobile-user-details">
                        <h3>${nickname}</h3>
                        <p>${isExpertText}</p>
                    </div>
                `;
                
                mobileMenuNav.innerHTML = `
                    <!-- 手机端搜索栏 -->
                    <div class="mobile-nav-section">
                        <div class="mobile-nav-title">搜索佛经</div>
                        <div class="mobile-search-box">
                            <input type="text" placeholder="搜索佛经文章..." class="mobile-search-input">
                            <button class="mobile-search-btn">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mobile-nav-section">
                        <div class="mobile-nav-title">我的功能</div>
                        ${isExpert ? `
                        <a href="/my-annotations" class="mobile-nav-item">
                            <i class="fa fa-edit mobile-nav-icon"></i>
                            <span class="mobile-nav-text">我的批注</span>
                        </a>
                        ` : ''}
                        <a href="/my-comments" class="mobile-nav-item">
                            <i class="fa fa-comments mobile-nav-icon"></i>
                            <span class="mobile-nav-text">我的评论</span>
                        </a>
                        <a href="#" class="mobile-nav-item" onclick="logoutWechat()">
                            <i class="fa fa-sign-out mobile-nav-icon"></i>
                            <span class="mobile-nav-text">退出微信授权</span>
                        </a>
                    </div>
                `;
            } else {
                // 未登录状态
                mobileUserInfo.innerHTML = `
                    <div class="mobile-user-avatar" onclick="handleLogin()">
                        <i class="fa fa-user-circle"></i>
                    </div>
                    <div class="mobile-user-details" onclick="handleLogin()">
                        <h3>未登录</h3>
                        <p>点击登录</p>
                    </div>
                `;
                
                mobileMenuNav.innerHTML = `
                    <!-- 手机端搜索栏 -->
                    <div class="mobile-nav-section">
                        <div class="mobile-nav-title">搜索佛经</div>
                        <div class="mobile-search-box">
                            <input type="text" placeholder="搜索佛经文章..." class="mobile-search-input">
                            <button class="mobile-search-btn">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mobile-nav-section">
                        <div class="mobile-nav-title">登录功能</div>
                        <a href="#" class="mobile-nav-item" onclick="handleLogin()">
                            <i class="fa fa-sign-in mobile-nav-icon"></i>
                            <span class="mobile-nav-text">微信登录</span>
                        </a>
                    </div>
                `;
            }
        }
        
        // 全局用户状态管理
        let currentUser = null;
        let userToken = localStorage.getItem('user_token') || sessionStorage.getItem('user_token');
        // 检查用户登录状态
        async function checkUserLoginStatus() {
            console.log("userToken", userToken);

            try {
                // 使用API统一管理工具调用用户信息接口
                const data = await API.get('/api/wechat/userinfo');
                console.log("userinfo响应:", data);
                
                if (data.code === 1 && data.data) {
                    currentUser = data.data;
                    updateUserStatus(true, currentUser);
                    console.log('用户已登录:', currentUser);
                    return true;
                } else {
                    currentUser = null;
                    userToken = null;
                    localStorage.removeItem('user_token');
                    sessionStorage.removeItem('user_token');
                    updateUserStatus(false);
                    console.log('用户未登录');
                    return false;
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                currentUser = null;
                userToken = null;
                localStorage.removeItem('user_token');
                sessionStorage.removeItem('user_token');
                updateUserStatus(false);
                return false;
            }
        }
        
        // 获取当前用户信息
        function getCurrentUser() {
            return currentUser;
        }
        
        // 更新用户状态
        function updateUserStatus(isLoggedIn, userInfo = null) {
            const userInfoElement = document.querySelector('.user-info');
            if (!userInfoElement) return;
            
            if (isLoggedIn) {
                // 使用传入的用户信息或默认值
                const nickname = userInfo ? userInfo.nickname : '微信用户';
                const avatar = userInfo ? userInfo.avatar : 'https://via.placeholder.com/40x40/A67B5B/ffffff?text=微';
                const isExpert = userInfo ? (userInfo.is_expert == 1) : false;
                
                // 已登录状态
                userInfoElement.innerHTML = `
                    <div class="user-avatar" onclick="toggleUserMenu()">
                        <img src="${avatar}" alt="用户头像" onerror="this.src='https://via.placeholder.com/40x40/A67B5B/ffffff?text=微'">
                    </div>
                    <div class="user-details" onclick="toggleUserMenu()">
                        <div class="user-name">${nickname}</div>
                        <div class="user-status">${isExpert ? '专家用户' : '普通用户'}</div>
                    </div>
                    <div class="user-dropdown">
                        ${isExpert ? `
                        <a href="#" class="dropdown-item" onclick="showMyAnnotations()">
                            <i class="fa fa-edit"></i>
                            我的批注
                        </a>
                        ` : ''}
                        <a href="#" class="dropdown-item" onclick="showMyComments()">
                            <i class="fa fa-comments"></i>
                            我的评论
                        </a>
                        <a href="#" class="dropdown-item" onclick="logoutWechat()">
                            <i class="fa fa-sign-out"></i>
                            退出微信授权
                        </a>
                    </div>
                `;
                
                // 更新手机端菜单为已登录状态
                updateMobileMenuStatus(true);
            } else {
                // 未登录状态
                userInfoElement.innerHTML = `
                    <div class="user-avatar" onclick="handleLogin()">
                        <i class="fa fa-user-circle"></i>
                    </div>
                    <div class="user-details" onclick="handleLogin()">
                        <div class="user-name">未登录</div>
                        <div class="user-status">点击登录</div>
                    </div>
                `;
                
                // 更新手机端菜单为未登录状态
                updateMobileMenuStatus(false);
            }
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 响应式切换函数
        function handleResize() {
            const header = document.querySelector('.header');
            const searchSection = document.querySelector('.search-section');
            const userInfo = document.querySelector('.user-info');
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const mobileMenu = document.getElementById('mobileMenu');
            
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                // 手机端模式
                if (searchSection) searchSection.style.display = 'none';
                if (userInfo) userInfo.style.display = 'none';
                if (mobileMenuToggle) mobileMenuToggle.style.display = 'flex';
                
                // 关闭手机端菜单
                if (mobileMenu && mobileMenu.classList.contains('show')) {
                    mobileMenu.classList.remove('show');
                    const menuToggle = document.querySelector('.mobile-menu-toggle');
                    if (menuToggle) menuToggle.classList.remove('active');
                    document.body.style.overflow = 'auto';
                }
                
                console.log('切换到手机端模式，窗口宽度:', window.innerWidth);
            } else {
                // PC端模式
                if (searchSection) searchSection.style.display = 'flex';
                if (userInfo) userInfo.style.display = 'flex';
                if (mobileMenuToggle) mobileMenuToggle.style.display = 'none';
                
                // 关闭手机端菜单
                if (mobileMenu && mobileMenu.classList.contains('show')) {
                    mobileMenu.classList.remove('show');
                    const menuToggle = document.querySelector('.mobile-menu-toggle');
                    if (menuToggle) menuToggle.classList.remove('active');
                    document.body.style.overflow = 'auto';
                }
                
                console.log('切换到PC端模式，窗口宽度:', window.innerWidth);
            }
        }

        // 搜索功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化隐私政策检查
            initializePrivacyCheck();
            
            // 检查用户登录状态
            checkUserLoginStatus();
            
            // 监听隐私政策页面消息
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'privacy_agreed') {
                    onPrivacyAgreed();
                } else if (event.data && event.data.type === 'privacy_disagreed') {
                    onPrivacyDisagreed();
                }
            });
            
            // 初始化响应式
            handleResize();
            
            // 页面加载完成后再次检查
            window.addEventListener('load', handleResize);
            
            // 监听窗口大小变化（使用防抖）
            window.addEventListener('resize', debounce(handleResize, 250));
            
            // 监听设备旋转
            window.addEventListener('orientationchange', debounce(handleResize, 500));
            
            // PC端搜索
            const searchInput = document.querySelector('.search-input');
            const searchBtn = document.querySelector('.search-btn');
            
            if (searchInput && searchBtn) {
                // 搜索按钮点击事件
                searchBtn.addEventListener('click', function() {
                    performSearch(searchInput);
                });
                
                // 回车键搜索
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch(searchInput);
                    }
                });
            }
            
            // 手机端搜索
            const mobileSearchInput = document.querySelector('.mobile-search-input');
            const mobileSearchBtn = document.querySelector('.mobile-search-btn');
            
            if (mobileSearchInput && mobileSearchBtn) {
                // 搜索按钮点击事件
                mobileSearchBtn.addEventListener('click', function() {
                    performSearch(mobileSearchInput);
                    // 搜索后关闭手机端菜单
                    toggleMobileMenu();
                });
                
                // 回车键搜索
                mobileSearchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch(mobileSearchInput);
                        // 搜索后关闭手机端菜单
                        toggleMobileMenu();
                    }
                });
            }
            
            function performSearch(inputElement) {
                const query = inputElement.value.trim();
                if (query) {
                    console.log('搜索:', query);
                    // 跳转到首页并传递搜索关键词
                    window.location.href = '/?keyword=' + encodeURIComponent(query);
                } else {
                    // 如果搜索框为空，跳转到首页显示所有文章
                    window.location.href = '/';
                }
            }
            
            // 显示我的批注
            function showMyAnnotations() {
                if (typeof layer !== 'undefined') {
                    layer.msg('我的批注功能开发中...', {icon: 1});
                } else {
                    alert('我的批注功能开发中...');
                }
                // 关闭用户下拉菜单
                const dropdown = document.querySelector('.user-dropdown');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            }
            
            // 显示我的评论
            function showMyComments() {
                if (typeof layer !== 'undefined') {
                    layer.msg('我的评论功能开发中...', {icon: 1});
                } else {
                    alert('我的评论功能开发中...');
                }
                // 关闭用户下拉菜单
                const dropdown = document.querySelector('.user-dropdown');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            }
        });
    </script>


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策</title>
    <link rel="stylesheet" href="/assets/libs/font-awesome/css/font-awesome.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .privacy-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .privacy-header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 2px solid #A67B5B;
            margin-bottom: 30px;
        }
        
        .privacy-title {
            font-size: 28px;
            color: #A67B5B;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .privacy-subtitle {
            font-size: 16px;
            color: #6c757d;
            font-weight: 400;
        }
        
        .privacy-content {
            padding: 0 20px;
        }
        
        .privacy-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            color: #A67B5B;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background: #A67B5B;
        }
        
        .section-content {
            font-size: 16px;
            line-height: 1.8;
            color: #555;
        }
        
        .section-content p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        .section-content ul {
            margin: 15px 0;
            padding-left: 25px;
        }
        
        .section-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: #f8f9fa;
            border-left: 4px solid #A67B5B;
            padding: 20px;
            margin: 20px 0;
            border-radius: 6px;
        }
        
        .highlight-box h4 {
            color: #A67B5B;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .contact-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .contact-info h4 {
            color: #856404;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        
        .contact-item i {
            color: #A67B5B;
            width: 20px;
        }
        
        .privacy-footer {
            text-align: center;
            padding: 30px 0;
            border-top: 1px solid #e9ecef;
            margin-top: 40px;
            color: #6c757d;
        }
        
        .privacy-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .privacy-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 120px;
            justify-content: center;
        }
        
        .privacy-btn-disagree {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        
        .privacy-btn-disagree:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .privacy-btn-agree {
            background: #A67B5B;
            color: white;
        }
        
        .privacy-btn-agree:hover:not(:disabled) {
            background: #8B4513;
            transform: translateY(-1px);
        }
        
        .privacy-btn-agree:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .last-updated {
            font-size: 14px;
            color: #6c757d;
            margin-top: 15px;
            text-align: center;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .privacy-container {
                margin: 10px;
                padding: 15px;
            }
            
            .privacy-title {
                font-size: 24px;
            }
            
            .privacy-content {
                padding: 0 10px;
            }
            
            .section-title {
                font-size: 18px;
            }
            
            .section-content {
                font-size: 15px;
            }
            
            .privacy-actions {
                flex-direction: column;
                gap: 10px;
            }
            
            .privacy-btn {
                width: 100%;
            }
        }
        
        @media (max-width: 480px) {
            .privacy-container {
                margin: 5px;
                padding: 10px;
            }
            
            .privacy-title {
                font-size: 20px;
            }
            
            .section-title {
                font-size: 16px;
            }
            
            .section-content {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="privacy-container">
        <div class="privacy-header">
            <h1 class="privacy-title">隐私政策</h1>
            <p class="privacy-subtitle">保护用户隐私 · 维护信息安全</p>
        </div>
        
        <div class="privacy-content" id="privacy-content">
            <!-- 动态加载的隐私政策内容 -->
            <div class="loading-container" style="text-align: center; padding: 50px 0;">
                <i class="fa fa-spinner fa-spin" style="font-size: 24px; color: #A67B5B;"></i>
                <p style="margin-top: 15px; color: #6c757d;">正在加载隐私政策...</p>
            </div>
        </div>
        
        <div class="privacy-footer">
            <div class="privacy-actions">
                <button class="privacy-btn privacy-btn-disagree" onclick="disagreeAndExit()">
                    <i class="fa fa-times"></i>
                    不同意并退出
                </button>
                <button class="privacy-btn privacy-btn-agree" id="privacyAgreeBtn" disabled onclick="agreeAndReturn()">
                    <i class="fa fa-check"></i>
                    <span id="agreeBtnText">5秒后可点击同意</span>
                </button>
            </div>
        </div>
    </div>
        
    <script>
        // 隐私政策API工具类
        class PrivacyPolicyAPI {
            constructor() {
                this.baseUrl = '/api/privacy';
            }

            async getPrivacyPolicy() {
                try {
                    const response = await fetch(`${this.baseUrl}/get`);
                    const data = await response.json();
                    
                    if (data.code === 1) {
                        return {
                            success: true,
                            data: data.data
                        };
                    } else {
                        return {
                            success: false,
                            message: data.msg
                        };
                    }
                } catch (error) {
                    return {
                        success: false,
                        message: '网络请求失败: ' + error.message
                    };
                }
            }

            formatContent(content) {
                // 将纯文本转换为HTML，保持换行
                return content.replace(/\n/g, '<br>');
            }
        }

        // 加载隐私政策内容
        async function loadPrivacyPolicy() {
            const privacyAPI = new PrivacyPolicyAPI();
            const privacyContent = document.getElementById('privacy-content');
            const lastUpdated = document.getElementById('last-updated');
            
                const result = await privacyAPI.getPrivacyPolicy();
                    // 显示隐私政策内容
                    privacyContent.innerHTML = `
                        <div class="privacy-section">
                            <div class="section-content">
                                ${privacyAPI.formatContent(result.data.content)}
                            </div>
                        </div>
                    `;
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 加载隐私政策内容
            loadPrivacyPolicy();
            
            // 原有的倒计时功能
            startPrivacyCountdown();
        });

        // 原有的倒计时功能
        function startPrivacyCountdown() {
            const agreeBtn = document.getElementById('privacyAgreeBtn');
            const agreeBtnText = document.getElementById('agreeBtnText');
            let countdown = 5;
            
            const timer = setInterval(() => {
                countdown--;
                agreeBtnText.textContent = countdown + '秒后可点击同意';
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    agreeBtn.disabled = false;
                    agreeBtnText.textContent = '我已阅读并同意';
                }
            }, 1000);
        }
        
        // 不同意并退出
        function disagreeAndExit() {
            if (confirm('您确定要退出吗？退出后将无法使用我们的服务。')) {
                // 保存不同意状态到localStorage
                localStorage.setItem('privacy_agreed', 'false');
                localStorage.setItem('privacy_disagreed_time', new Date().toISOString());
                
                // 通知父窗口用户不同意隐私政策
                if (window.opener) {
                    if (window.opener.onPrivacyDisagreed) {
                        window.opener.onPrivacyDisagreed();
                    } else {
                        // 备用方案：发送消息
                        window.opener.postMessage({
                            type: 'privacy_disagreed',
                            action: 'disagree'
                        }, '*');
                    }
                }
                
                window.close();
                // 如果无法关闭窗口，则返回上一页
                if (!window.closed) {
                    window.history.back();
                }
            }
        }
        
        // 同意并返回
        function agreeAndReturn() {
            // 存储用户同意的状态
            localStorage.setItem('privacy_agreed', 'true');
            localStorage.setItem('privacy_agreed_time', new Date().toISOString());
            
            // 通知父窗口用户已同意隐私政策
            if (window.opener) {
                // 如果是弹窗打开的，调用父窗口的同意处理函数
                if (window.opener.onPrivacyAgreed) {
                    window.opener.onPrivacyAgreed();
                } else {
                    // 备用方案：发送消息
                    window.opener.postMessage({
                        type: 'privacy_agreed',
                        action: 'agree'
                    }, '*');
                }
            }
            
            // 关闭窗口或返回
            if (window.opener) {
                window.close();
            } else {
                window.history.back();
            }
        }
        
        // 平滑滚动到顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // 添加返回顶部按钮
        const scrollTopBtn = document.createElement('button');
        scrollTopBtn.innerHTML = '<i class="fa fa-chevron-up"></i>';
        scrollTopBtn.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: #A67B5B;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            box-shadow: 0 4px 12px rgba(166,123,91,0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        `;
        
        scrollTopBtn.addEventListener('click', scrollToTop);
        scrollTopBtn.addEventListener('mouseenter', function() {
            this.style.background = '#8B4513';
            this.style.transform = 'translateY(-2px)';
        });
        scrollTopBtn.addEventListener('mouseleave', function() {
            this.style.background = '#A67B5B';
            this.style.transform = 'translateY(0)';
        });
        
        document.body.appendChild(scrollTopBtn);
        
        // 监听滚动显示/隐藏返回顶部按钮
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollTopBtn.style.display = 'flex';
            } else {
                scrollTopBtn.style.display = 'none';
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>个人中心 - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
        }
        .nav-link:hover {
            color: white !important;
        }
        .profile-header {
            background: white;
            padding: 40px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #e9ecef;
            margin: 0 auto 20px;
            display: block;
        }
        .profile-name {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .profile-bio {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }
        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        .menu-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.07);
        }
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 25px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
        }
        .menu-item:last-child {
            border-bottom: none;
        }
        .menu-item:hover {
            background: #f8f9fa;
            text-decoration: none;
            color: #007bff;
            padding-left: 10px;
        }
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.1rem;
        }
        .menu-content {
            flex: 1;
        }
        .menu-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .menu-desc {
            color: #666;
            font-size: 0.9rem;
        }
        .menu-arrow {
            color: #ccc;
            font-size: 1.2rem;
        }
        .update-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
            margin-top: 20px;
        }
        .update-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .logout-btn:hover {
            background: #c82333;
            color: white;
            text-decoration: none;
        }
        .recent-activity {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.07);
        }
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #e3f2fd;
            color: #1976d2;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        .activity-content {
            flex: 1;
        }
        .activity-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .activity-time {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fa fa-book"></i> {$site.name|htmlentities}
            </a>
            
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="annotation.html">专家批注</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="user.html">个人中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">退出</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 个人资料头部 -->
    <section class="profile-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <img src="__CDN__/assets/img/avatar1.jpg" class="profile-avatar" alt="用户头像">
                    <h2 class="profile-name">张教授</h2>
                    <p class="profile-bio">人工智能专家 | 清华大学计算机系教授</p>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <div class="stat-number">15</div>
                            <div class="stat-label">批注文章</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">234</div>
                            <div class="stat-label">总批注数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">1,567</div>
                            <div class="stat-label">被阅读</div>
                        </div>
                    </div>
                    
                    <button class="btn update-btn" onclick="updateProfile()">
                        <i class="fa fa-refresh"></i> 一键授权更新头像、昵称
                    </button>
                </div>
                <div class="col-md-4">
                    <div class="text-right">
                        <button class="btn logout-btn" onclick="logout()">
                            <i class="fa fa-sign-out"></i> 退出登录
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <!-- 功能菜单 -->
                <section class="menu-section">
                    <h3 class="section-title">个人中心</h3>
                    
                    <a href="user-annotations.html" class="menu-item">
                        <div class="menu-icon">
                            <i class="fa fa-edit"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-title">我的批注（专家）</div>
                            <div class="menu-desc">查看和管理您发表的所有批注</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fa fa-chevron-right"></i>
                        </div>
                    </a>
                    
                    <a href="user-comments.html" class="menu-item">
                        <div class="menu-icon">
                            <i class="fa fa-comments"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-title">我的评论</div>
                            <div class="menu-desc">查看您发表的所有评论</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fa fa-chevron-right"></i>
                        </div>
                    </a>
                    
                    <a href="user-profile.html" class="menu-item">
                        <div class="menu-icon">
                            <i class="fa fa-user"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-title">个人资料</div>
                            <div class="menu-desc">修改个人信息和头像</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fa fa-chevron-right"></i>
                        </div>
                    </a>
                    
                    <a href="user-changepwd.html" class="menu-item">
                        <div class="menu-icon">
                            <i class="fa fa-lock"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-title">修改密码</div>
                            <div class="menu-desc">更改您的登录密码</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fa fa-chevron-right"></i>
                        </div>
                    </a>
                    
                    <a href="privacy.html" class="menu-item">
                        <div class="menu-icon">
                            <i class="fa fa-shield"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-title">隐私政策</div>
                            <div class="menu-desc">网站的用户必须同意才能使用本系统的一个免责声明</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fa fa-chevron-right"></i>
                        </div>
                    </a>
                </section>
            </div>
            
            <div class="col-md-4">
                <!-- 最近活动 -->
                <section class="recent-activity">
                    <h3 class="section-title">最近活动</h3>
                    
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fa fa-edit"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">批注了文章《人工智能在教育领域的应用》</div>
                            <div class="activity-time">2小时前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fa fa-comment"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">评论了文章《区块链技术应用》</div>
                            <div class="activity-time">1天前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fa fa-thumbs-up"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">支持了文章《可持续发展与绿色技术》</div>
                            <div class="activity-time">2天前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fa fa-edit"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">批注了文章《数字化转型分析》</div>
                            <div class="activity-time">3天前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fa fa-user"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">更新了个人资料</div>
                            <div class="activity-time">1周前</div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
    <script>
        function updateProfile() {
            // 调用微信授权更新用户信息
            if (typeof wx !== 'undefined') {
                wx.getUserInfo({
                    success: function(res) {
                        alert('头像和昵称更新成功！');
                        location.reload();
                    }
                });
            } else {
                alert('请在微信环境中使用');
            }
        }
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>微信登录 - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .login-subtitle {
            color: #666;
            font-size: 1rem;
        }
        .wechat-login-btn {
            background: #07c160;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s;
            margin-bottom: 20px;
        }
        .wechat-login-btn:hover {
            background: #06ad56;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(7, 193, 96, 0.3);
            color: white;
            text-decoration: none;
        }
        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: all 0.3s;
            width: 100%;
        }
        .back-btn:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        .qr-code {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .qr-code i {
            font-size: 4rem;
            color: #07c160;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">微信登录</h1>
                <p class="login-subtitle">使用微信账号快速登录</p>
            </div>
            
            <div class="qr-code">
                <i class="fa fa-qrcode"></i>
                <p class="mt-3 text-muted">请使用微信扫描二维码登录</p>
            </div>
            
            <button class="btn wechat-login-btn" onclick="wechatLogin()">
                <i class="fa fa-wechat"></i> 微信登录
            </button>
            
            <a href="index.html" class="btn back-btn">
                <i class="fa fa-arrow-left"></i> 返回首页
            </a>
        </div>
    </div>

    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
    <script>
        function wechatLogin() {
            // 模拟微信登录
            alert('正在跳转到微信登录...');
            setTimeout(function() {
                alert('登录成功！');
                window.location.href = 'user.html';
            }, 2000);
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>专家批注 - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
        }
        .nav-link:hover {
            color: white !important;
        }
        .page-header {
            background: white;
            padding: 40px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }
        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        .article-item {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.07);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        .article-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .article-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        .article-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        .article-title a {
            color: #333;
            text-decoration: none;
        }
        .article-title a:hover {
            color: #007bff;
        }
        .article-meta {
            color: #666;
            font-size: 0.9rem;
        }
        .article-summary {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .annotation-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .annotation-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.07);
            margin-bottom: 30px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }
        .stats-label {
            color: #666;
            font-size: 1rem;
        }
        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.07);
        }
        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            color: #666;
            padding: 8px 20px;
            border-radius: 20px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        .filter-btn.active {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }
        .filter-btn:hover {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }
        .expert-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .expert-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            margin-right: 12px;
            border: 2px solid #e9ecef;
        }
        .expert-name {
            color: #007bff;
            font-weight: 600;
        }
        .expert-title {
            color: #666;
            font-size: 0.9rem;
        }
        .annotation-count {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fa fa-book"></i> {$site.name|htmlentities}
            </a>
            
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="annotation.html">专家批注</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="user.html">个人中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">
                            <i class="fa fa-wechat"></i> 微信登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 页面头部 -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">专家批注</h1>
            <p class="page-subtitle">选择文章进行批注，包括新增内容、修改内容、删除内容</p>
        </div>
    </section>

    <div class="container">
        <!-- 统计信息 -->
        <div class="row">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number">156</div>
                    <div class="stats-label">待批注文章</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number">45</div>
                    <div class="stats-label">已批注文章</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number">234</div>
                    <div class="stats-label">批注总数</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <section class="filter-section">
            <h4 class="mb-3">筛选条件</h4>
            <button class="btn filter-btn active" data-filter="all">全部文章</button>
            <button class="btn filter-btn" data-filter="unannotated">未批注</button>
            <button class="btn filter-btn" data-filter="annotated">已批注</button>
            <button class="btn filter-btn" data-filter="recent">最近更新</button>
        </section>

        <!-- 文章列表 -->
        <section class="articles-list">
            <!-- 文章1 -->
            <div class="article-item" data-category="unannotated">
                <div class="article-header">
                    <div>
                        <h3 class="article-title">
                            <a href="article.html">
                                人工智能在教育领域的应用与发展趋势
                            </a>
                        </h3>
                        <div class="article-meta">
                            <i class="fa fa-calendar"></i> 2024-01-15 | 
                            <i class="fa fa-user"></i> 张教授 | 
                            <i class="fa fa-eye"></i> 1,234 次阅读
                        </div>
                    </div>
                    <span class="annotation-count">0 条批注</span>
                </div>
                
                <div class="expert-info">
                    <img src="__CDN__/assets/img/avatar1.jpg" class="expert-avatar" alt="专家头像">
                    <div>
                        <div class="expert-name">张教授</div>
                        <div class="expert-title">人工智能专家 | 清华大学</div>
                    </div>
                </div>
                
                <p class="article-summary">
                    本文深入探讨了人工智能技术在教育领域的应用现状，分析了未来发展趋势，为教育工作者提供了有价值的参考。文章从个性化学习、智能教学助手等多个角度进行了详细分析...
                </p>
                
                <div class="text-right">
                    <a href="annotation-add.html" class="btn annotation-btn">
                        <i class="fa fa-edit"></i> 添加批注
                    </a>
                </div>
            </div>

            <!-- 文章2 -->
            <div class="article-item" data-category="annotated">
                <div class="article-header">
                    <div>
                        <h3 class="article-title">
                            <a href="article.html">
                                区块链技术在供应链管理中的创新应用
                            </a>
                        </h3>
                        <div class="article-meta">
                            <i class="fa fa-calendar"></i> 2024-01-14 | 
                            <i class="fa fa-user"></i> 李博士 | 
                            <i class="fa fa-eye"></i> 856 次阅读
                        </div>
                    </div>
                    <span class="annotation-count">8 条批注</span>
                </div>
                
                <div class="expert-info">
                    <img src="__CDN__/assets/img/avatar2.jpg" class="expert-avatar" alt="专家头像">
                    <div>
                        <div class="expert-name">李博士</div>
                        <div class="expert-title">区块链专家 | 北京大学</div>
                    </div>
                </div>
                
                <p class="article-summary">
                    探讨区块链技术如何改变传统供应链管理模式，提高透明度和效率，降低运营成本。文章结合实际案例，分析了区块链在供应链中的具体应用场景...
                </p>
                
                <div class="text-right">
                    <a href="annotation-add.html" class="btn annotation-btn">
                        <i class="fa fa-edit"></i> 继续批注
                    </a>
                </div>
            </div>

            <!-- 文章3 -->
            <div class="article-item" data-category="unannotated">
                <div class="article-header">
                    <div>
                        <h3 class="article-title">
                            <a href="article.html">
                                可持续发展与绿色技术的未来展望
                            </a>
                        </h3>
                        <div class="article-meta">
                            <i class="fa fa-calendar"></i> 2024-01-13 | 
                            <i class="fa fa-user"></i> 王研究员 | 
                            <i class="fa fa-eye"></i> 1,567 次阅读
                        </div>
                    </div>
                    <span class="annotation-count">0 条批注</span>
                </div>
                
                <div class="expert-info">
                    <img src="__CDN__/assets/img/avatar3.jpg" class="expert-avatar" alt="专家头像">
                    <div>
                        <div class="expert-name">王研究员</div>
                        <div class="expert-title">环境科学专家 | 中科院</div>
                    </div>
                </div>
                
                <p class="article-summary">
                    分析当前环境挑战，探讨绿色技术创新在推动可持续发展中的重要作用。文章从政策、技术、经济等多个维度分析了绿色发展的路径...
                </p>
                
                <div class="text-right">
                    <a href="annotation-add.html" class="btn annotation-btn">
                        <i class="fa fa-edit"></i> 添加批注
                    </a>
                </div>
            </div>

            <!-- 文章4 -->
            <div class="article-item" data-category="annotated">
                <div class="article-header">
                    <div>
                        <h3 class="article-title">
                            <a href="article.html">
                                数字化转型对企业竞争力的影响分析
                            </a>
                        </h3>
                        <div class="article-meta">
                            <i class="fa fa-calendar"></i> 2024-01-12 | 
                            <i class="fa fa-user"></i> 陈专家 | 
                            <i class="fa fa-eye"></i> 2,341 次阅读
                        </div>
                    </div>
                    <span class="annotation-count">20 条批注</span>
                </div>
                
                <div class="expert-info">
                    <img src="__CDN__/assets/img/avatar4.jpg" class="expert-avatar" alt="专家头像">
                    <div>
                        <div class="expert-name">陈专家</div>
                        <div class="expert-title">数字化转型专家 | 咨询公司</div>
                    </div>
                </div>
                
                <p class="article-summary">
                    研究数字化转型如何重塑企业运营模式，提升竞争力，适应新时代发展需求。文章通过大量案例分析，为企业数字化转型提供了实用指导...
                </p>
                
                <div class="text-right">
                    <a href="annotation-add.html" class="btn annotation-btn">
                        <i class="fa fa-edit"></i> 继续批注
                    </a>
                </div>
            </div>

            <!-- 文章5 -->
            <div class="article-item" data-category="recent">
                <div class="article-header">
                    <div>
                        <h3 class="article-title">
                            <a href="article.html">
                                量子计算在密码学中的应用前景
                            </a>
                        </h3>
                        <div class="article-meta">
                            <i class="fa fa-calendar"></i> 2024-01-16 | 
                            <i class="fa fa-user"></i> 刘教授 | 
                            <i class="fa fa-eye"></i> 432 次阅读
                        </div>
                    </div>
                    <span class="annotation-count">0 条批注</span>
                </div>
                
                <div class="expert-info">
                    <img src="__CDN__/assets/img/avatar5.jpg" class="expert-avatar" alt="专家头像">
                    <div>
                        <div class="expert-name">刘教授</div>
                        <div class="expert-title">量子计算专家 | 中科大</div>
                    </div>
                </div>
                
                <p class="article-summary">
                    探讨量子计算技术对传统密码学的挑战和机遇，分析后量子密码学的发展方向。文章从技术原理到实际应用进行了深入分析...
                </p>
                
                <div class="text-right">
                    <a href="annotation-add.html" class="btn annotation-btn">
                        <i class="fa fa-edit"></i> 添加批注
                    </a>
                </div>
            </div>
        </section>

        <!-- 加载更多 -->
        <div class="text-center mt-4">
            <button class="btn btn-outline-primary btn-lg">
                <i class="fa fa-refresh"></i> 加载更多文章
            </button>
        </div>
    </div>

    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
    <script>
        // 筛选功能
        $('.filter-btn').click(function() {
            $('.filter-btn').removeClass('active');
            $(this).addClass('active');
            
            var filter = $(this).data('filter');
            
            if (filter === 'all') {
                $('.article-item').show();
            } else {
                $('.article-item').hide();
                $('.article-item[data-category="' + filter + '"]').show();
            }
        });
    </script>
</body>
</html> 
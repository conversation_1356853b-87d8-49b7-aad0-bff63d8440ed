<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Log;

/**
 * 调试控制器
 */
class Debug extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 调试首页
     */
    public function index()
    {
        // 示例数据
        $data = [
            'name' => '调试测试',
            'time' => time(),
            'array' => [1, 2, 3, 4, 5],
            'object' => (object)['key' => 'value']
        ];
        
        // 方法1：使用dump() - 会停止执行并显示调试信息
        // dump($data);
        
        // 方法2：使用dump()但不停止执行
        dump($data, false);
        
        // 方法3：使用var_dump()
        // var_dump($data);
        
        // 方法4：使用print_r()
        // print_r($data);
        
        // 方法5：记录到日志
        Log::write('调试数据：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        
        // 方法6：使用trace()记录到Trace面板
        trace('调试信息：' . json_encode($data), 'debug');
        
        // 方法7：在模板中调试
        $this->assign('debug_data', $data);
        $this->assign('title', '调试示例');
        
        return $this->view->fetch();
    }
    
    /**
     * Ajax调试
     */
    public function ajax()
    {
        // 获取请求参数
        $data = $this->request->param();
        
        // 记录到日志
        Log::write('Ajax请求数据：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        
        // 返回调试信息
        return json([
            'code' => 1,
            'msg' => '调试信息',
            'data' => $data,
            'time' => time()
        ]);
    }
    
    /**
     * 数据库调试
     */
    public function database()
    {
        try {
            // 执行数据库查询
            $users = \think\Db::name('user')->limit(5)->select();
            
            // 记录SQL查询
            Log::write('数据库查询结果：' . json_encode($users, JSON_UNESCAPED_UNICODE));
            
            // 使用trace记录
            trace('数据库查询：' . json_encode($users), 'sql');
            
            return json([
                'code' => 1,
                'msg' => '数据库调试',
                'data' => $users
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '数据库错误：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 错误调试
     */
    public function error()
    {
        // 故意制造一个错误
        $undefined = $undefined_variable;
        
        return '这里不会执行到';
    }
    
    /**
     * 性能调试
     */
    public function performance()
    {
        $start_time = microtime(true);
        
        // 模拟一些操作
        for ($i = 0; $i < 1000; $i++) {
            $result = $i * $i;
        }
        
        $end_time = microtime(true);
        $execution_time = $end_time - $start_time;
        
        // 记录性能信息
        Log::write('性能测试：执行时间 ' . $execution_time . ' 秒');
        trace('性能测试：执行时间 ' . $execution_time . ' 秒', 'performance');
        
        return json([
            'code' => 1,
            'msg' => '性能测试完成',
            'execution_time' => $execution_time
        ]);
    }
} 
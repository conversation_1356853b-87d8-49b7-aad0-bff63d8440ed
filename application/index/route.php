<?php

use think\Route;

// 前台路由配置
// 首页
Route::get('/', 'index/Index/index');

// 文章相关
Route::get('article', 'index/Index/article');
Route::get('article/:id', 'index/Index/article');
Route::get('article/:id/:title', 'index/Index/article');
Route::get('article/:id/:title', 'index/Index/article');
Route::get('privacy', 'index/User/privacy');

// API接口
// Route::group('api', function () {
//     // 投票
//     Route::post('vote', 'api/Index/vote');
    
//     // 评论
//     Route::get('comments/:article_id', 'api/Index/comments');
//     Route::post('comment', 'api/Index/comment');
    
//     // 批注
//     Route::get('annotations/:article_id', 'api/Index/annotations');
//     Route::post('annotation/add', 'api/Index/addAnnotation');
//     Route::post('annotation/edit', 'api/Index/editAnnotation');
//     Route::post('annotation/delete', 'api/Index/deleteAnnotation');
    
//     // 用户
//     Route::post('user/login', 'api/User/login');
//     Route::post('user/register', 'api/User/register');
//     Route::post('user/logout', 'api/User/logout');
//     Route::post('user/profile', 'api/User/profile');
    
//     // 微信
//     Route::post('wechat/login', 'api/Wechat/login');
//     Route::post('wechat/callback', 'api/Wechat/callback');
// });


<?php

namespace app\api\library;

use app\common\library\Auth as CommonAuth;
use app\common\library\Token;
use app\common\model\Users;
use think\Hook;

/**
 * API专用Auth类，支持Users表
 */
class Auth extends CommonAuth
{
    /**
     * 根据Token初始化
     *
     * @param string $token Token
     * @return boolean
     */
    public function init($token)
    {
        if ($this->_logined) {
            return true;
        }
        if ($this->_error) {
            return false;
        }
        $data = Token::get($token);
        if (!$data) {
            return false;
        }
        $user_id = intval($data['user_id']);
        if ($user_id > 0) {
            // 使用Users模型而不是User模型
            $user = Users::get($user_id);
            if (!$user) {
                $this->setError('Account not exist');
                return false;
            }
            // 检查用户状态，Users表中status=1表示正常
            if ($user['status'] != 1) {
                $this->setError('Account is locked');
                return false;
            }
            $this->_user = $user;
            $this->_logined = true;
            $this->_token = $token;

            //初始化成功的事件
            Hook::listen("user_init_successed", $this->_user);

            return true;
        } else {
            $this->setError('You are not logged in');
            return false;
        }
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    public function getId()
    {
        return $this->_user ? $this->_user['id'] : 0;
    }

    /**
     * 获取当前用户信息
     * @return array|null
     */
    public function getUser()
    {
        return $this->_user;
    }
}

<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\api\library\Auth;

/**
 * API基础控制器，使用自定义Auth类支持Users表
 */
class Base extends Api
{
    public function _initialize()
    {
        //跨域请求检测
        check_cors_request();

        // 检测IP是否允许
        check_ip_allowed();

        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');

        // 使用自定义的Auth类来支持Users表
        $this->auth = Auth::instance();

        $modulename = $this->request->module();
        $controllername = \think\Loader::parseName($this->request->controller());
        $actionname = strtolower($this->request->action());

        // token - 支持多种方式获取token
        $token = $this->request->server('HTTP_TOKEN', 
                 $this->request->request('token', 
                 \think\Cookie::get('token')));

        $path = str_replace('.', '/', $controllername) . '/' . $actionname;
        // 设置当前请求的URI
        $this->auth->setRequestUri($path);
        // 检测是否需要验证登录
        if (!$this->auth->match($this->noNeedLogin)) {
            //初始化
            $this->auth->init($token);
            //检测是否登录
            if (!$this->auth->isLogin()) {
                $this->error(__('Please login first'), null, 401);
            }
            // 判断是否需要验证权限
            if (!$this->auth->match($this->noNeedRight)) {
                // 判断控制器和方法判断是否有对应权限
                if (!$this->auth->check($path)) {
                    $this->error(__('You have no permission'), null, 403);
                }
            }
        } else {
            // 如果有传递token才验证是否登录状态
            if ($token) {
                $this->auth->init($token);
            }
        }
    }
}

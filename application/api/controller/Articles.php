<?php

namespace app\api\controller;

use app\common\model\Articles as ArticlesModel;

/**
 * 文章接口
 */
class Articles extends Base
{
    protected $noNeedLogin = ['index', 'detail', 'list'];
    protected $noNeedRight = '*';

    /**
     * 文章列表
     * GET /api/articles/list?page=1&pagesize=10&keyword=xxx
     */
    public function index()
    {
        $page = (int)$this->request->get('page', 1);
        $page = $page > 0 ? $page : 1;
        $pageSize = (int)$this->request->get('pagesize', 10);
        $pageSize = $pageSize > 0 ? min($pageSize, 50) : 10;
        $keyword = trim((string)$this->request->get('keyword', ''));

        // 使用 table 显式指定完整表名，避免全局前缀（如 fa_）影响
        $query = ArticlesModel::where('status', 1);
        if ($keyword !== '') {
            $query->where('title', 'like', '%' . $keyword . '%');
        }

        $paginator = $query
            ->field('id,title,cover_image,introduction,read_count,annotation_count,created_at')
            ->order('created_at', 'desc')
            ->paginate($pageSize, false, ['page' => $page]);

        $items = array_map(function ($row) {
            return is_object($row) && method_exists($row, 'toArray') ? $row->toArray() : $row;
        }, $paginator->items());

        $this->success('ok', [
            'items'     => $items,
            'total'     => (int)$paginator->total(),
            'page'      => (int)$paginator->currentPage(),
            'pagesize'  => (int)$paginator->listRows(),
        ]);
    }

    /**
     * 文章列表 (list方法，直接调用index)
     * GET /api/articles/list?page=1&pagesize=10&keyword=xxx
     */
    public function list()
    {
        return $this->index();
    }

    /**
     * 文章详情
     * GET /api/articles/{id}
     * @param int $id
     */
    public function detail($id = 0)
    {
        $articleId = (int)($id ?: $this->request->param('id/d', 0));
        if ($articleId <= 0) {
            $this->error('参数错误', null, 422);
        }

        $article = ArticlesModel::where('id', $articleId)
            ->where('status', 1)
            ->find();

        if (!$article) {
            $this->error('文章不存在', null, 404);
        }

        // 自增阅读数（不影响返回）
        try {
            ArticlesModel::where('id', $articleId)->setInc('read_count', 1);
            // 确保返回为数组
            $article = is_object($article) && method_exists($article, 'toArray') ? $article->toArray() : (array)$article;
            $article['read_count'] = (int)$article['read_count'] + 1;
        } catch (\Throwable $e) {
            // 忽略统计失败
        }

        $this->success('ok', $article);
    }
}


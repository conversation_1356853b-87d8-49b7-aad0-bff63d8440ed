<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Config;
use think\Db;

/**
 * 隐私政策API接口
 */
class Privacy extends Api
{
    protected $noNeedLogin = ['get', 'getLatest'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header('Access-Control-Expose-Headers: __token__');
        }
        //跨域检测
        check_cors_request();

        if (!isset($_COOKIE['PHPSESSID'])) {
            Config::set('session.id', $this->request->server("HTTP_SID"));
        }
        parent::_initialize();
    }

    /**
     * 获取隐私政策内容
     * @ApiMethod (GET)
     */
    public function get()
    {
        // try {
            // 获取站点配置中的隐私政策
            $privacyPolicy = Config::get('site.privacyPolicy');
            
            if (!$privacyPolicy) {
                $this->error('隐私政策未配置');
            }
            
            $data = [
                'content' => $privacyPolicy,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $this->success('获取成功', $data);
            
        // } catch (\Exception $e) {
            // $this->error('获取隐私政策失败：' . $e->getMessage());
        // }
    }
} 
<?php

namespace app\api\controller;

use fast\Random;
use think\Db;
use app\common\model\Users;
use app\common\library\Token;

/**
 * 模拟微信登录授权
 */
class Wechat extends Base
{
    protected $noNeedLogin = ['login', 'status', 'confirm', 'logout'];
    protected $noNeedRight = '*';


    /**
     * 获取模拟二维码登录信息
     * @ApiMethod (GET)
     */
    public function login()
    {
        // 生成模拟state
        $state = Random::uuid();
        // 标记状态为待确认，5分钟过期
        cache('wx_state_' . $state, ['status' => 'pending'], 300);

        // 使用一个占位二维码图片（项目已有资源）
        $qrImage = cdnurl('/assets/img/40px.png', true);

        $this->success('ok', [
            'state'       => $state,
            'qrcode_url'  => $qrImage,
            'expires_in'  => 300,
        ]);
    }

    /**
     * 轮询登录状态
     * @ApiMethod (GET)
     * @ApiParams (name="state", type="string", required=true, description="登录态state")
     */
    public function status()
    {
        $state = $this->request->get('state');
        if (!$state) {
            $this->error('Invalid parameters');
        }
        $data = cache('wx_state_' . $state);
        if (!$data) {
            $this->success('expired', ['status' => 'expired']);
        }
        if (($data['status'] ?? 'pending') === 'success') {
            $user = null;
            $token = null;
            if (!empty($data['user_id'])) {
                $user = Users::where('id', (int)$data['user_id'])->find();
                // 如果缓存中已有token，直接使用；否则生成新token
                if ($user) {
                    if (!empty($data['token'])) {
                        $token = $data['token'];
                    } else {
                        // 使用FastAdmin的Token类生成token
                        $token = Random::uuid();
                        Token::set($token, $user['id'], 86400); // 24小时有效期

                        // 更新缓存中的token
                        $data['token'] = $token;
                        cache('wx_state_' . $state, $data, 300);
                    }

                    // 调试信息
                    error_log("Token used in polling: " . $token . " for user: " . $user['id']);
                }
            }
            $this->success('ok', [
                'status' => 'success',
                'token'  => $token,
                'user'   => $user,
            ]);
        }
        $this->success('ok', ['status' => 'pending']);
    }

    /**
     * 模拟扫码确认并登录
     * @ApiMethod (POST)
     * @ApiParams (name="state", type="string", required=true, description="登录态state")
     * @ApiParams (name="nickname", type="string", required=false, description="昵称(可选)")
     * @ApiParams (name="avatar", type="string", required=false, description="头像URL(可选)")
     */
    public function confirm()
    {
        $state = $this->request->post('state');
        $nickname = $this->request->post('nickname', '微信用户' . date('His'));
        $avatar = $this->request->post('avatar', '');
        $privacyAgreed = $this->request->post('privacy_agreed', '0');

        if (!$state) {
            $this->error('Invalid parameters');
        }

        $cached = cache('wx_state_' . $state);
        if (!$cached) {
            $this->error('State expired');
        }

        // 必须强制同意隐私政策
        if ((string)$privacyAgreed !== '1') {
            $this->error('必须先同意隐私政策');
        }

        // 以state派生稳定 openid（模拟）
        $openid = 'sim_' . substr(md5($state), 0, 28);

        // 写入/更新到自定义 users 表（真实执行 SQL）
        $now = date('Y-m-d H:i:s');
        $exists = Users::where('openid', $openid)->find();
        if ($exists) {
            Users::where('id', $exists['id'])->update([
                'nickname'   => $nickname,
                'avatar'     => $avatar,
                'status'     => 1,
                'privacy_agreed' => 1,
                'updated_at' => $now,
            ]);
            $userId = (int)$exists['id'];
        } else {
            $userId = Users::insertGetId([
                'openid'      => $openid,
                'nickname'    => $nickname,
                'avatar'      => $avatar,
                'is_expert'   => 0,
                'status'      => 1,
                'privacy_agreed' => 1,
                // 隐私是否同意留给前端/后续接口处理，这里不强制写入
                'created_at'  => $now,
                'updated_at'  => $now,
            ]);
        }

        // 使用FastAdmin的Token类生成token
        $token = Random::uuid();
        Token::set($token, $userId, 86400); // 24小时有效期

        // 调试信息
        error_log("Token generated: " . $token . " for user: " . $userId);

        // 标记该 state 登录完成，供轮询接口返回 success
        cache('wx_state_' . $state, ['status' => 'success', 'openid' => $openid, 'user_id' => $userId, 'token' => $token], 300);

        $this->success('ok', [
            'token' => $token,
            'user' => [
                'id'       => $userId,
                'openid'   => $openid,
                'nickname' => $nickname,
                'avatar'   => $avatar,
            ],
        ]);
    }
    
    /**
     * 获取当前用户信息
     * @ApiMethod (GET)
     */
    public function UserInfo()
    {
        // 使用自定义的Auth类获取当前用户
        if (!$this->auth->isLogin()) {
            $this->error('用户未登录', ['user' => null]);
        }

        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('用户不存在', ['user' => null]);
        }

        $this->success('ok', [
                'id'       => $user['id'],
                'openid'   => $user['openid'],
                'nickname' => $user['nickname'],
                'avatar'   => $user['avatar'],
                'is_expert' => $user['is_expert'],
                'status'   => $user['status'],
                'privacy_agreed' => $user['privacy_agreed'],
        ]);
    }
    
    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        // 使用FastAdmin的Auth类退出登录
        if ($this->auth->isLogin()) {
            $token = $this->auth->getToken();
            Token::delete($token);
        }

        $this->success('退出成功');
    }
}


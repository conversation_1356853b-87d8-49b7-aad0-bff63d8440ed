-- 专家批注文章系统 - 完整数据库脚本
-- 创建时间: 2024年
-- 说明: 包含所有表结构和索引优化

-- =============================================
-- 0. 删除已存在的表（如果存在）
-- =============================================

DROP TABLE IF EXISTS `votes`;
DROP TABLE IF EXISTS `comments`;
DROP TABLE IF EXISTS `annotations`;
DROP TABLE IF EXISTS `articles`;
DROP TABLE IF EXISTS `users`;

-- =============================================
-- 1. 创建数据库表结构
-- =============================================

-- 1.1 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '微信头像',
  `is_expert` tinyint(1) DEFAULT 0 COMMENT '是否为专家 0-否 1-是',
  `status` tinyint(1) DEFAULT 1 COMMENT '用户状态 0-冻结 1-正常',
  `privacy_agreed` tinyint(1) DEFAULT 0 COMMENT '是否同意隐私政策 0-否 1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 1.2 文章表
CREATE TABLE `articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '文章标题',
  `original_content` longtext NOT NULL COMMENT '原始文章内容',
  `annotated_content` longtext DEFAULT NULL COMMENT '批注后的文章内容',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `introduction` varchar(500) DEFAULT NULL COMMENT '文章介绍',
  `read_count` int(11) DEFAULT 0 COMMENT '阅读数量',
  `annotation_count` int(11) DEFAULT 0 COMMENT '批注数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '文章状态 0-下架 1-正常',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 1.3 专家批注表
CREATE TABLE `annotations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `expert_id` int(11) NOT NULL COMMENT '专家用户ID',
  `type` tinyint(1) NOT NULL COMMENT '批注类型 1-新增 2-修改 3-删除',
  `annotation_text` text NOT NULL COMMENT '批注内容',
  `target_text` text DEFAULT NULL COMMENT '被批注的目标文本',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `article_id` (`article_id`),
  KEY `expert_id` (`expert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专家批注表';

-- 1.4 评论表
CREATE TABLE `comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `parent_id` int(11) DEFAULT 0 COMMENT '父评论ID 0-顶级评论',
  `child_id` int(11) DEFAULT 0 COMMENT '子评论ID 0-无子评论',
  `content` text NOT NULL COMMENT '评论内容',
  `status` tinyint(1) DEFAULT 1 COMMENT '评论状态 0-删除 1-正常',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `article_id` (`article_id`),
  KEY `user_id` (`user_id`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 1.5 投票表
CREATE TABLE `votes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `annotation_id` int(11) DEFAULT NULL COMMENT '文章批注ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `vote_type` tinyint(1) NOT NULL COMMENT '投票类型 1-支持 2-有疑问',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_article_vote` (`user_id`, `article_id`, `vote_type`),
  UNIQUE KEY `user_annotation_vote` (`user_id`, `annotation_id`, `vote_type`),
  KEY `article_id` (`article_id`),
  KEY `annotation_id` (`annotation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='投票表';

-- =============================================
-- 2. 索引优化
-- =============================================

-- 2.1 文章表索引优化
ALTER TABLE `articles` ADD INDEX `idx_status_created` (`status`, `created_at`);

-- 2.2 专家批注表索引优化
ALTER TABLE `annotations` ADD INDEX `idx_article_expert` (`article_id`, `expert_id`);

-- 2.3 评论表索引优化
ALTER TABLE `comments` ADD INDEX `idx_article_status` (`article_id`, `status`);

-- 2.4 投票表索引优化
ALTER TABLE `votes` ADD INDEX `idx_article_type` (`article_id`, `vote_type`);
ALTER TABLE `votes` ADD INDEX `idx_annotation_type` (`annotation_id`, `vote_type`);

-- =============================================
-- 3. 验证脚本执行结果
-- =============================================

-- 查看创建的表
SHOW TABLES;

-- 查看表结构
DESCRIBE users;
DESCRIBE articles;
DESCRIBE annotations;
DESCRIBE comments;
DESCRIBE votes;

-- 查看索引
SHOW INDEX FROM articles;
SHOW INDEX FROM annotations;
SHOW INDEX FROM comments;
SHOW INDEX FROM votes; 